package feature.dashboard.lockhistory

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import data.common.preferences.Preferences
import data.network.android.models.AccessLogs
import data.utils.android.CommonValues
import data.utils.android.settings.SharedPreferenceUtils
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.SharedAdapterLayoutBinding

class SharedAdapter :
    RecyclerView.Adapter<SharedAdapter.ViewHolder>() {

    lateinit var context: Context
    var arrayHistory = ArrayList<AccessLogs>()

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = SharedAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(arrayHistory[position])
    }

    override fun getItemCount() = arrayHistory.size

    @SuppressLint("NotifyDataSetChanged")
    fun updateList(it1: ArrayList<AccessLogs>) {
        arrayHistory = it1
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: SharedAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(model: AccessLogs){
            binding.txtPersonsName.text = model.user_name
            binding.txtDateLimit.text = CommonValues.formateTimeDate(model.valid_from, context) + " - "+CommonValues.formateTimeDate(
                model.valid_to,
                context
            )
            if (model.share_status == 0) {
                if (!model.isSystemRevoked && model.usernameShareBy == "") {
                    binding.txtAccessStatus.setTextColor(
                        context.getColor(keyless.feature.common.R.color.red_negative)
                    )
                    binding.txtAccessStatus.text = context.getString(keyless.data.utils.android.R.string.expired)
                    binding.revokeReason.isVisible = false
                    binding.txtDate.text =CommonValues.formateTimeDate(model.valid_to, context)
                }else if (model.isSystemRevoked || model.usernameShareBy != "") {
                    binding.txtAccessStatus.setTextColor(
                        context.getColor(keyless.feature.common.R.color.red_negative)
                    )
                    binding.revokeReason.isVisible = true
                    binding.txtAccessStatus.text = context.getString(keyless.data.utils.android.R.string.revoked)
                    if (model.isSystemRevoked) {
                        if (Preferences.userRole.get() == CommonValues.INTEGRATOR ) {
                            binding.revokeReasonPm.isVisible = true
                            binding.revokeReasonHeading.isVisible = true
                            binding.revokeReasonPm.text = model.revokeBy
                        }else{
                            binding.revokeReasonPm.isVisible = false
                            binding.revokeReasonHeading.isVisible = false
                        }
                        binding.revokeReason.text = "(By Keyless System)"
                    }else{
                        binding.revokeReason.text = "(By " + model.revokeBy + ")"
                    }
                    if (model.revokeOn.contains("+") || model.revokeOn.contains(".")){
                        binding.txtDate.text = CommonValues.formateTimeDate(model.revokeOn.split(".")[0],context)
                    }else{
                        binding.txtDate.text = CommonValues.formateTimeDate(model.revokeOn,context)
                    }
                }
            } else if (model.share_status == 1){
                binding.txtAccessStatus.setTextColor(
                    context.getColor(keyless.feature.common.R.color.green_positive)
                )
                binding.txtAccessStatus.text = context.getString(keyless.data.utils.android.R.string.granted)
                binding.revokeReason.isVisible = false
                if (model.created_at.contains("+") || model.created_at.contains(".")){
                    binding.txtDate.text = CommonValues.formateTimeDate(model.created_at.split(".")[0],context)
                }else{
                    binding.txtDate.text = CommonValues.formateTimeDate(model.created_at,context)
                }
            }
        }
    }
}