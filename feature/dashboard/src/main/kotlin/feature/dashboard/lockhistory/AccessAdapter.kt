package feature.dashboard.lockhistory

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import data.network.android.models.AccessLogs
import data.utils.android.CommonValues
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.AccessAdapterLayoutBinding

class AccessAdapter() : RecyclerView.Adapter<AccessAdapter.ViewHolder>() {
    lateinit var context: Context
    var arrayHistory = ArrayList<AccessLogs>()

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = AccessAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(arrayHistory[position])
    }

    override fun getItemCount() = arrayHistory.size

    @SuppressLint("NotifyDataSetChanged")
    fun updateList(accessLogs: ArrayList<AccessLogs>) {
        arrayHistory = accessLogs
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: AccessAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: AccessLogs) {
            binding.txtPersonsName.text = model.user_name
            binding.txtDeviceName.text = model.device_model
            if (model.created_at.contains("+") || model.created_at.contains(".")) {
                binding.txtDate.text = CommonValues.formateTimeDate(
                    model.created_at.split(".")[0],
                    context
                )
            } else {
                binding.txtDate.text = CommonValues.formateTimeDate(model.created_at, context)
            }

            if (model.status == "0") {
                binding.ivStatus.setImageResource(R.drawable.iv_access_denied)
            } else {
                binding.ivStatus.setImageResource(R.drawable.ic_tick)
            }
        }
    }
}