package feature.dashboard.shareaccess

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import data.network.android.UserSharedModel
import data.utils.android.CommonValues
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.SharedAccessWithLayBinding

class AdapterAccessSharedWith(context: Context) :
    RecyclerView.Adapter<AdapterAccessSharedWith.ViewHolder>() {

    lateinit var context: Context
    var listMain: ArrayList<UserSharedModel> = ArrayList()
    var listener = context as ClickOnDelete

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = SharedAccessWithLayBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("NewApi", "SetTextI18n")
    @RequiresApi(Build.VERSION_CODES.N)
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listMain[position])
    }

    override fun getItemCount() = listMain.size
    fun updateData(users: ArrayList<UserSharedModel>) {
        listMain = users
        notifyDataSetChanged()
    }

    inner class ViewHolder(val binding: SharedAccessWithLayBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: UserSharedModel) {
            if (model.detail.first_name.isNotEmpty()) {
                binding.txtName.isVisible = true
                binding.txtName.text =
                    model.detail.first_name + " " + model.detail.last_name
            } else if (model.detail.name.isNotEmpty()) {
                binding.txtName.isVisible = true
                binding.txtName.text = model.detail.name
            } else {
                binding.txtName.isVisible = false
            }

            if (model.detail.email.isNotEmpty()) {
                binding.txtEmail.isVisible = true
                binding.txtEmail.text = model.detail.email
            } else {
                binding.txtEmail.isVisible = false
            }

            if (!model.detail.mobile_number.isNullOrEmpty()) {
                binding.txtPhone.isVisible = true
                binding.txtPhone.text = model.detail.country_code + " " + model.detail.mobile_number
            } else {
                binding.txtPhone.isVisible = false
            }

            if (model.assignment_data.valid_from.isNotEmpty()) {
                binding.txtDateRange.isVisible = true
                binding.txtDateRange.setTextColor(
                    ContextCompat.getColor(context, keyless.feature.common.R.color.black)
                )
                binding.txtDateRange.text =
                    CommonValues.formateTimeDate(model.assignment_data.valid_from, context) +
                    " to " + CommonValues.formateTimeDate(model.assignment_data.valid_to, context)
            } else if (model.status == "pending") {
                binding.txtDateRange.text = "Pending Invite"
                binding.txtDateRange.setTextColor(
                    ContextCompat.getColor(context, keyless.feature.common.R.color.red_negative)
                )
            } else {
                binding.txtDateRange.isVisible = false
                binding.txtDateRange.setTextColor(
                    ContextCompat.getColor(context, keyless.feature.common.R.color.black)
                )
            }

            if (model.assignment_data.passcodeId.isNotEmpty()) {
                binding.txtPasscode.isVisible = true
                binding.txtPasscode.text = context.getString(R.string.passcode) + ": "
                binding.txtPasscodeValue.isVisible = true
                binding.txtPasscodeValue.text = model.assignment_data.passcode
            } else {
                binding.txtPasscode.visibility = View.GONE
                binding.txtPasscodeValue.visibility = View.GONE
            }

            binding.deleteAccess.setOnClickListener {
                listener.clickForDelete(model)
            }

            binding.mainLay.setOnClickListener {
                listener.clickForEdit(model)
            }
        }
    }

    interface ClickOnDelete {
        fun clickForDelete(detail: UserSharedModel)
        fun clickForEdit(detail: UserSharedModel)
    }
}