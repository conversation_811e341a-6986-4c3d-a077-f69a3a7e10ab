<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.dashboard.lockinfo.LockInfoActivity">

<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="16dp"-->
<!--        android:minHeight="?attr/actionBarSize"-->
<!--        android:theme="?attr/actionBarTheme"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent">-->

<!--        <TextView-->
<!--            android:id="@+id/textView2"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:fontFamily="@font/poppins_medium_500"-->
<!--            android:gravity="center"-->
<!--            android:text="Lock Info"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="18sp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="@+id/toolbar"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <ImageView-->
<!--            android:id="@+id/ivEditLock"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="end"-->
<!--            android:layout_marginEnd="20dp"-->
<!--            android:background="@drawable/ic_edit"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/textView2"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_back"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="match_parent"-->
<!--            android:background="@drawable/iv_back_white"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@+id/imageView2"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->
<!--    </androidx.appcompat.widget.Toolbar>-->

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:minHeight="?attr/actionBarSize"
        android:theme="?attr/actionBarTheme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/lock_info"
                android:textColor="@color/white"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                style="@style/ImageMirror"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/iv_back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <View
                android:id="@+id/viewBack"
                android:layout_width="30dp"
                android:layout_height="30dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivEditLock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="20dp"
                android:background="@drawable/ic_edit"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintVertical_bias="1.0">

        <FrameLayout
            android:id="@+id/frameLayout"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="25dp"
            android:background="@drawable/bg_button_rounded"
            android:backgroundTint="@color/white"
            android:elevation="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/placeHolder"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:padding="22dp"
                android:src="@drawable/iv_other_icon" />

        </FrameLayout>

        <TextView
            android:id="@+id/lockName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text=""
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/frameLayout" />


        <ImageView
            android:visibility="invisible"
            android:id="@+id/markIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/marker"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lockName"/>

        <TextView
            android:id="@+id/locAddressTV"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="-4dp"
            android:layout_marginEnd="20dp"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/fetching_location"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/markIcon"
            app:layout_constraintTop_toTopOf="@+id/markIcon" />



        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginTop="14dp"
            android:id="@+id/rv_lock_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/locAddressTV" />

        <TextView
            android:id="@+id/locTxt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/location"
            android:textColor="@color/black"
            android:textSize="18dp"
            app:layout_constraintStart_toStartOf="@+id/rv_lock_info"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock_info" />

        <androidx.cardview.widget.CardView
            android:id="@+id/mapCard"
            android:layout_width="0dp"
            android:layout_height="167dp"
            android:layout_marginEnd="20dp"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            android:elevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/locTxt"
            app:layout_constraintTop_toBottomOf="@+id/locTxt">

            <FrameLayout
                android:id="@+id/lockInfoMapFrame"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </androidx.cardview.widget.CardView>

        <ProgressBar
            android:id="@+id/progressLockInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>