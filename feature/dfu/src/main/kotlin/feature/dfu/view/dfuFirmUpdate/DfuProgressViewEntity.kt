package feature.dfu.view.dfuFirmUpdate

import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import feature.dfu.data.Uploading

sealed class DFUProgressViewEntity {
    companion object {
        fun createBootloaderStage() = WorkingProgressViewEntity(
            ProgressItemViewEntity(bootloaderStatus = ProgressItemStatus.WORKING)
        )

        fun createDfuStage() = WorkingProgressViewEntity(
            ProgressItemViewEntity(
                bootloaderStatus = ProgressItemStatus.SUCCESS,
                dfuStatus = ProgressItemStatus.WORKING
            )
        )

        fun createInstallingStage(progress: Uploading) = WorkingProgressViewEntity(
            ProgressItemViewEntity(
                bootloaderStatus = ProgressItemStatus.SUCCESS,
                dfuStatus = ProgressItemStatus.SUCCESS,
                installationStatus = ProgressItemStatus.WORKING,
                progress = progress
            )
        )

        fun createSuccessStage() = WorkingProgressViewEntity(
            ProgressItemViewEntity(
                bootloaderStatus = ProgressItemStatus.SUCCESS,
                dfuStatus = ProgressItemStatus.SUCCESS,
                installationStatus = ProgressItemStatus.SUCCESS,
                resultStatus = ProgressItemStatus.SUCCESS
            )
        )

        fun ProgressItemViewEntity.createErrorStage(errorMessage: String?) = WorkingProgressViewEntity(
            ProgressItemViewEntity(
                bootloaderStatus = bootloaderStatus.createErrorStatus(),
                dfuStatus = dfuStatus.createErrorStatus(),
                installationStatus = installationStatus.createErrorStatus(),
                resultStatus = ProgressItemStatus.ERROR,
                errorMessage = errorMessage
            )
        )

        fun ProgressItemStatus.createErrorStatus() = when (this) {
            ProgressItemStatus.SUCCESS -> ProgressItemStatus.SUCCESS
            else -> ProgressItemStatus.ERROR
        }
    }
}

object DisabledProgressViewEntity : DFUProgressViewEntity()

data class WorkingProgressViewEntity(
    val status: ProgressItemViewEntity = ProgressItemViewEntity()
) : DFUProgressViewEntity()

data class ProgressItemViewEntity(
    val bootloaderStatus: ProgressItemStatus = ProgressItemStatus.DISABLED,
    val dfuStatus: ProgressItemStatus = ProgressItemStatus.DISABLED,
    val installationStatus: ProgressItemStatus = ProgressItemStatus.DISABLED,
    val resultStatus: ProgressItemStatus = ProgressItemStatus.DISABLED,
    val progress: Uploading = Uploading(),
    val errorMessage: String? = null
) {
    fun isRunning() = !isCompleted() && (
        bootloaderStatus != ProgressItemStatus.DISABLED ||
            dfuStatus != ProgressItemStatus.DISABLED ||
            installationStatus == ProgressItemStatus.WORKING
        )
    fun isCompleted() =
        resultStatus == ProgressItemStatus.SUCCESS || resultStatus == ProgressItemStatus.ERROR
}

data class ProgressItemLabel(
    @StringRes val idleText: Int,
    @StringRes val workingText: Int,
    @StringRes val successText: Int
) {
    @Composable
    fun toDisplayString(status: ProgressItemStatus): String {
        return when (status) {
            ProgressItemStatus.WORKING -> stringResource(id = workingText)
            ProgressItemStatus.SUCCESS -> stringResource(id = successText)
            ProgressItemStatus.DISABLED,
            ProgressItemStatus.ERROR -> stringResource(id = idleText)
        }
    }
}

val BootloaderItem = ProgressItemLabel(
    keyless.data.utils.android.R.string.dfu_bootloader_idle,
    keyless.data.utils.android.R.string.dfu_bootloader_working,
    keyless.data.utils.android.R.string.dfu_bootloader_success
)

val DfuItem = ProgressItemLabel(
    keyless.data.utils.android.R.string.dfu_process_idle,
    keyless.data.utils.android.R.string.dfu_process_working,
    keyless.data.utils.android.R.string.dfu_process_success
)

val FirmwareItem = ProgressItemLabel(
    keyless.data.utils.android.R.string.dfu_firmware_idle,
    keyless.data.utils.android.R.string.dfu_firmware_working,
    keyless.data.utils.android.R.string.dfu_firmware_success
)