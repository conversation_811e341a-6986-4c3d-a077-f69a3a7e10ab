package feature.dfu.view.dfuFirmUpdate

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Bluetooth
import androidx.compose.runtime.Composable
import feature.dfu.data.DeviceTarget

sealed class DFUSelectDeviceViewEntity

internal object DisabledSelectedDeviceViewEntity : DFUSelectDeviceViewEntity()

internal object NotSelectedDeviceViewEntity : DFUSelectDeviceViewEntity()

internal data class SelectedDeviceViewEntity(val target: DeviceTarget) : DFUSelectDeviceViewEntity()

private val icon = Icons.Outlined.Bluetooth

@Composable
internal fun DFUSelectedDeviceView(
    viewEntity: DFUSelectDeviceViewEntity,
    enabled: Boolean,
    onEvent: (DfuViewEvent) -> Unit
) {
    when (viewEntity) {
        DisabledSelectedDeviceViewEntity -> {}
        NotSelectedDeviceViewEntity -> {}
        is SelectedDeviceViewEntity -> {}
    }
}