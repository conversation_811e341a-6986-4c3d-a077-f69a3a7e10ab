package feature.common.icons

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.network.android.ApiUtils
import data.network.android.models.IconModel
import keyless.feature.common.databinding.SelectIconAdapterLayoutBinding

class SelectIconAdapter(context: Context, listener: SetIcon = context as SetIcon) :
    RecyclerView.Adapter<SelectIconAdapter.ViewHolder>(), Filterable {

    private lateinit var dialogNew: Dialog
    lateinit var context: Context
    private var listener = listener
    var arrayIconsNew = ArrayList<IconModel>()
    var listFiltered = ArrayList<IconModel>()

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = SelectIconAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position], position)
    }

    override fun getItemCount() = listFiltered.size

    @SuppressLint("NotifyDataSetChanged")
    fun updateValues(arrayIcons: ArrayList<IconModel>, dialog: Dialog) {
        arrayIconsNew = arrayIcons
        listFiltered = arrayIcons
        dialogNew = dialog
        notifyDataSetChanged()
    }

//    class ViewHolder(view: View) : RecyclerView.ViewHolder(view)

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    arrayIconsNew
                } else {
                    val mFilteredList = ArrayList<IconModel>()
                    arrayIconsNew.filter {
                        (it.name.contains(constraint!!, true)) or
                            (it.name.startsWith(constraint, true))
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as ArrayList<IconModel>
                }
                notifyDataSetChanged()
            }
        }
    }

    inner class ViewHolder(val binding: SelectIconAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(landmark: IconModel, position: Int) {
            if (landmark.icon.isNotEmpty()) {
                Glide.with(context)
                    .load(ApiUtils.IMAGE_BASE_URL + listFiltered[position].icon)
                    .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                    .into(binding.ivIcons)
            }
            binding.txtIconName.text = listFiltered[position].name

            binding.mainLay.setOnClickListener {
                dialogNew.dismiss()
                listener.getIcon(listFiltered[position])
            }
        }
    }

    interface SetIcon {
        fun getIcon(iconModelAll: IconModel)
    }
}