<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:theme="@style/Theme.MaterialComponents.Light"
    card_view:cardBackgroundColor="@color/border_color"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    android:id="@+id/mainLay"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true"
    card_view:strokeColor="@color/border_color"
    card_view:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="start"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivIconHome"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="25dp"
            android:visibility="gone"
            tools:ignore="MissingConstraints" />
      <!--      TODO Remove Suppress MissingConstraints -->


      <TextView
            android:id="@+id/txtLockModel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            style="@style/mirrorText"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="16dp"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintHorizontal_bias="0.0"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txtLockName"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:layout_marginStart="16dp"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            card_view:layout_constraintEnd_toEndOf="@+id/txtLockModel"
            card_view:layout_constraintHorizontal_bias="0.0"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockModel" />

        <TextView
            android:id="@+id/txtLockAddress"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockName" />


        <TextView
            android:id="@+id/txtLockBrand"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="14dp"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockAddress"
            card_view:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/txtLockInstalledVersion"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="gone"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockBrand"
            card_view:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/txt_lock_available_version"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/whatsapp"
            android:textSize="14dp"
            android:visibility="gone"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txtLockInstalledVersion"
            card_view:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/txtAccess"
            style="@style/mirrorText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="14dp"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toBottomOf="@+id/txt_lock_available_version"
            card_view:layout_constraintVertical_bias="0.0" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
