<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/white_all_corners_10"
    android:layout_gravity="center"
    android:layout_marginStart="20dp"
    android:layout_marginEnd="20dp"
    card_view:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_groceries"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/select_icon"
            android:textColor="@color/black"
            android:textSize="18sp"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/sv_lock"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="15dp"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/bg_edit_grey"
            android:drawableStart="@drawable/ic_baseline_search_24"
            android:drawablePadding="10dp"
            android:ellipsize="end"
            style="@style/mirrorText"
            android:fontFamily="@font/poppins_regular_400"
            android:hint="@string/search_by_name"
            android:includeFontPadding="false"
            android:padding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingRight="30dp"
            android:singleLine="true"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.recyclerview.widget.RecyclerView
            android:layout_marginBottom="15dp"
            android:id="@+id/rv_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sv_lock"
            card_view:layout_constraintVertical_bias="0.0" />

        <TextView
            android:visibility="gone"
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="60dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/save"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </LinearLayout>

</androidx.core.widget.NestedScrollView>