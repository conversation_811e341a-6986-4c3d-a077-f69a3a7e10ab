package feature.regula.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DocumentModel(

    @SerializedName("ChipPage") var ChipPage: Int? = null,
    @SerializedName("ContainerList") var ContainerList: ContainerList? = ContainerList(),
    @SerializedName("ProcessingFinished") var ProcessingFinished: Int? = null,
    @SerializedName("TransactionInfo") var TransactionInfo: TransactionInfo? = TransactionInfo(),
    @SerializedName("elapsedTime") var elapsedTime: Int? = null,
    @SerializedName("lightType") var lightType: ArrayList<Int> = arrayListOf(),
    @SerializedName("morePagesAvailable") var morePagesAvailable: Int? = null,
    @SerializedName("processCommandRes") var processCommandRes: Int? = null,
    @SerializedName("resolutionType") var resolutionType: Int? = null

)