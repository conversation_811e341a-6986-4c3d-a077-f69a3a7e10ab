package feature.masterkey

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.fragment.app.Fragment
import com.blekey.sdk.ble.BleScanCallback
import com.blekey.sdk.ble.BluetoothLeScan
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADD_ADAPTER
import data.utils.android.CommonValues.Companion.EXTRA_BLE_NAME
import data.utils.android.common.BleLockScanData
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import dots.animation.textview.TextAndAnimationView
import keyless.feature.master.key.R
import keyless.feature.master.key.databinding.FragmentFindMasterKeyBinding

class FindMasterKeyFragment : Fragment() {

    private var mBleName: ArrayList<String?> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var REQUEST_CHECK_SETTINGS = 3
    var fromWhere = 0
    lateinit var dialogLock: Dialog
    private lateinit var binding: FragmentFindMasterKeyBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentFindMasterKeyBinding.inflate(layoutInflater)
        return binding.root
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        methodRequiresTwoPermission()
        fromWhere = 0
        clickListeners()
    }

    private fun methodRequiresTwoPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[2] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                        if (fromWhere == 1) {
                            initData()
                        }
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    val lm =
                        requireActivity().getSystemService(AppCompatActivity.LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                            if (fromWhere == 1) {
                                initData()
                            }
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            }
        }
    }

    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(requireActivity())
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        if (fromWhere == 1) {
                            initData()
                        }
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        startIntentSenderForResult(
                            status.resolution!!.intentSender,
                            REQUEST_CHECK_SETTINGS,
                            null,
                            0,
                            0,
                            0,
                            null
                        )

//                        status.startResolutionForResult(
//                            requireActivity(),
//                            REQUEST_CHECK_SETTINGS
//                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            if (fromWhere == 1) {
                initData()
            }
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT) {
            if (!CommonValues.isBluetoothEnabled()) {
                val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (!mBluetoothAdapter.isEnabled) {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                } else {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                }
            } else {
                if (fromWhere == 1) {
                    initData()
                }
            }
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    private fun showDialogForPermissions() {
        var locPermission = ActivityCompat.checkSelfPermission(
            requireActivity(),
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        if (locPermission == -1) {
            defaultDialog(
                requireActivity(),
                "Please allow permissions to continue",
                object : OnActionOK {
                    override fun onClickData() {
                        if (activity != null && isAdded) {
                            startActivityForResult(
                                Intent(
                                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                    Uri.fromParts("package", requireActivity().packageName, null)
                                ),
                                10
                            )
                        }
                    }
                }
            )
        }
    }

    private fun clickListeners() {
        binding.txtBtnNext.setOnClickListener {
            methodRequiresTwoPermission()
            fromWhere = 1
        }

        binding.backBtnProfile.setOnClickListener {
            if (mBluetoothLeScan != null) {
                mBluetoothLeScan!!.stopReceiver()
                requireActivity().finish()
            } else {
                requireActivity().finish()
            }
        }
    }

    private fun initData() {
        showDialogForScanning()
        mBleName = ArrayList()
        mBleName.add(EXTRA_BLE_NAME)
        mBluetoothLeScan =
            BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback)
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        mBluetoothLeScan!!.startReceiver()
        isScan = true
    }

    private fun showDialogForScanning() {
        dialogLock = Dialog(requireActivity())
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        var animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        var textForScanning = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.textForScanning)
        var txtConnecting = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.txtConnecting)
        textForScanning.text =
            getString(keyless.data.utils.android.R.string.you_must_be_near_the_key_to_perform_this_action)
        txtConnecting.text = getString(keyless.data.utils.android.R.string.detecting_key)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }
        cancelBtn.setOnClickListener {
            mBluetoothLeScan!!.stopReceiver()
            dialogLock.dismiss()
        }
        dialogLock.show()
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        override fun openBluetooth() {}

        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            var name: String? = ""
            name = if (bluetoothDevice.name == null) {
                ""
            } else {
                bluetoothDevice.name
            }
            val bleLockScanData = BleLockScanData(name!!, bluetoothDevice.address, scanRecord)
            mBleHandler!!.obtainMessage(
                ADD_ADAPTER,
                bleLockScanData
            ).sendToTarget()
        }

        override fun finishScan() {
        }
    }

    override fun onDetach() {
        super.onDetach()
        if (mBluetoothLeScan != null) {
            mBluetoothLeScan!!.stopReceiver()
        }
    }

    override fun onStop() {
        super.onStop()
        if (mBluetoothLeScan != null) {
            mBluetoothLeScan!!.stopReceiver()
        }
    }

    @SuppressLint("MissingPermission")
    private fun addAdapterItem(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItems(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItems(bleBeans: BleLockScanData) {
        if (bleBeans == null) {
            return
        }
        dialogLock.dismiss()
        mBluetoothLeScan!!.stopReceiver()
        val bundle = Bundle()
        bundle.putParcelable("keyName", bleBeans)

        CommonValues.loadFragmentWithStack(
            feature.masterkey.ConfigureMasterKeyFragment(),
            bundle,
            requireFragmentManager(),
            R.id.frameContainerMaster
        )
    }

    private fun checkName(name: String): Boolean {
        if (null == name) return false
        for (ble in mBleName) {
            if (name.contains(ble!!)) {
                return true
            }
        }
        return false
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            addAdapterItem(msg.obj as BleLockScanData)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
//                    if (!running) {
//                        running = true
//                        countDownTimer = object : CountDownTimerExt(20000, 100) {
//                            override fun onTimerTick(millisUntilFinished: Long) {
//                                if (msg.obj != null) {
//                                    try {
//                                        addAdapterItem(msg.obj as BleLockScanData)
//                                    } catch (e: Exception) {
//                                        e.printStackTrace()
//                                    }
//                                }
//                            }
//
//                            override fun onTimerFinish() {
//                                if (layProgress != null) {
//                                    layProgress.isVisible = false
//                                    defaultDialog(
//                                        requireActivity(),
//                                        getString(keyless.data.utils.android.R.string.scanned_key_could_not_found),
//                                        object : OnActionOK {
//                                            override fun onClickData() {
//                                                mBluetoothLeScan!!.stopReceiver()
//                                                countDownTimer = null
//
//                                            }
//                                        })
//                                }
//                            }
//
//                        }
//                        countDownTimer?.start()
//
//                    }
                }
            }
        }
    }
}