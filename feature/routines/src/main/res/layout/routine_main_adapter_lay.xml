<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/mainLay"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/txtName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/office_lock"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/txtPersons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableStart="@drawable/iv_persons"
        android:drawablePadding="6dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:visibility="gone"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="@+id/txtName"
        app:layout_constraintTop_toBottomOf="@+id/txtName" />


    <TextView
        android:id="@+id/txtDoors"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:drawableStart="@drawable/iv_door_routine"
        android:drawablePadding="6dp"
        android:visibility="gone"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        app:layout_constraintBottom_toBottomOf="@+id/txtPersons"
        app:layout_constraintStart_toEndOf="@+id/txtPersons"
        app:layout_constraintTop_toTopOf="@+id/txtPersons" />


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="18dp"
        android:background="@color/line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtPersons"
        app:layout_constraintVertical_bias="0.0" />


    <ImageView
        android:id="@+id/arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_forward"
        android:visibility="gone"
        style="@style/ImageMirror"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3" />

</androidx.constraintlayout.widget.ConstraintLayout>