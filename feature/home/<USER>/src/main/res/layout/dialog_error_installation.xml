<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/textView33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/error_in_installation"
        android:textSize="18dp"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView35"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:text="@string/please_put_your_comments_below_spo_the_admin_can_be_notified_the_error"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView33" />


    <EditText
        android:id="@+id/etIssue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        style="@style/mirrorText"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/explain_the_issue_here"
        android:includeFontPadding="false"
        android:padding="14dp"
        android:textColor="@color/black"
        android:textSize="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView35" />


    <View
        android:id="@+id/view4"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/line_bg_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etIssue" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.5"
        android:orientation="vertical" />



    <TextView
        android:id="@+id/txtMarkAsRead"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/mark_as_failed"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline6"
        app:layout_constraintTop_toBottomOf="@+id/view4"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/cancelBtn"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:text="@string/text_cancel"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view4"
        app:layout_constraintVertical_bias="0.0" />


    <View
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="@color/line_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline6"
        app:layout_constraintStart_toStartOf="@+id/guideline6"
        app:layout_constraintTop_toBottomOf="@+id/view4" />


</androidx.constraintlayout.widget.ConstraintLayout>