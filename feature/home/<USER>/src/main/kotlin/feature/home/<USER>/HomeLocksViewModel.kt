package feature.home.locks

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import data.network.android.AdminLocksResponse
import data.network.android.ApiUtils
import data.keyless.authentication.models.SendOtpResponse
import data.network.android.ModelMessage
import presentation.common.domain.repositories.ErrorMessageHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class HomeLocksViewModel : ViewModel() {

    private val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress
    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    private var _getResponseAdminLocks = MutableLiveData<AdminLocksResponse>()
    val getResponseAdminLocks: LiveData<AdminLocksResponse> = _getResponseAdminLocks
    private var _deleteLock = MutableLiveData<SendOtpResponse>()
    val deleteLock: LiveData<SendOtpResponse> = _deleteLock

    fun hitAdminLocksApi(token: String, progress: Boolean, page: Int, search: String) {
        if (!progress) {
            _progress.value = false
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.adminLocksChinese(token, page, search, {
                (it as AdminLocksResponse)
                _progress.value = false
                _getResponseAdminLocks.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun hitAdminDeleteLock(token: String, id: String) {
//        _progress.value = false
//        _progress.value = true
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.adminDeleteLocksChinese(
                token,
                id,
                {
                    (it as SendOtpResponse)
                    _progress.value = false
                    _deleteLock.value = it
                },
                {
                    _progress.value = false
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }
            )
        }
    }

    fun getCheckUser(token: String, jsonObject: JsonObject, isProgress: Boolean) = liveData {
        if (isProgress) {
            _progress.value = true
        }
        ApiUtils.getCheckUser(
            token,
            jsonObject,
            {
                (it as ModelMessage)
                _progress.value = false
                emit(it)
            },
            {
//            _progress.value = false
//            _error.value = ApiErrorHandler.handleException(it).toString()
            }
        )
    }

    fun claimKey(token: String, jsonObject: JsonObject) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.claimKey(token, jsonObject, {
            _progress.value = false
            emit(it as ModelMessage)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun getServicesApi(token: String, jsonObject: JsonObject) = liveData {
        ApiUtils.getServicesApi(token, jsonObject, {
            emit(it as ModelMessage)
        }, {
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }
}