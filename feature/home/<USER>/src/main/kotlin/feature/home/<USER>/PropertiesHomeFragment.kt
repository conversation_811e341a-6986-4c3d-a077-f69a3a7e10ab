package feature.home.properties

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.LocksListResponse
import data.utils.android.CommonValues
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.hideKeyboard
import data.utils.android.settings.SharedPreferenceUtils
import feature.dashboard.unlock.LockDetailsActivity
import keyless.feature.home.properties.databinding.FragmentPropertiesHomeBinding
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class PropertiesHomeFragment : Fragment(), LockAdapter.ClickToEnter {

    private lateinit var adapterProperties: PropertiesAdapter
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    private lateinit var binding: FragmentPropertiesHomeBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentPropertiesHomeBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUpListView()
        clickEvents()
    }

    private fun clickEvents() {
        binding.svProperties.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
//                if (p0.toString().isNotEmpty()){
                adapterProperties.filter.filter(p0.toString())
                binding.stopSearch.isVisible = p0.toString().isNotEmpty()

//                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.svProperties.setText("")
            adapterProperties.filter.filter("")
        }
    }

    private fun setUpListView() {
        val homeLocks = sharePrefs.getLockData()
        val homeProperties = sharePrefs.getPropertiesData()
        if (homeProperties.size > 0) {
            binding.svProperties.isVisible = true
            binding.lvLock.isVisible = true
            binding.noDataProperties.isVisible = false
            for (i in homeProperties) {
                var countMain = 0
                for (j in homeLocks) {
                    if (i._id == j.property_details.id) {
                        countMain++
                    }
                }
                i.count = countMain
            }

            binding.lvLock.layoutManager = LinearLayoutManager(context)
            adapterProperties = PropertiesAdapter(
                homeProperties,
                homeLocks,
                this@PropertiesHomeFragment
            )
            binding.lvLock.adapter = adapterProperties
        } else {
            binding.svProperties.isVisible = false
            binding.lvLock.isVisible = false
            binding.noDataProperties.isVisible = true
        }
    }

    @SuppressLint("SimpleDateFormat")
    override fun clicking(locksModel: LocksListResponse.LocksModel) {
        if (
            Preferences.userRole.get() == CommonValues.GUEST ||
            Preferences.role.get() == Roles.SYSTEM_MANAGER ||
            Preferences.role.get() == Roles.VIEWER_ACCESS ||
            Preferences.role.get() == Roles.CUSTOMER_SERVICES
        ) {
            val startDate =
                CommonValues.formattedDateOnlyEn(
                    locksModel.assignment?.assignment_data?.valid_from!!.split("+")[0]
                )

            var dateToDisplay = CommonValues.formatDdMmYyyyy(
                locksModel.assignment?.assignment_data?.valid_from!!.split(
                    "+"
                )[0],
                requireActivity()
            )

            var currentDate = ""

            currentDate = if (CommonValues.isNetworkAvailable(requireActivity())) {
                if (!CommonValues.serverDateTime.isNullOrEmpty()) {
                    CommonValues.formattedDateOnlyEn(
                        CommonValues.serverDateTime.split(
                            "."
                        )[0]
                    )
                } else {
                    val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                    sdf.format(Date())
                }
            } else {
                val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                sdf.format(Date())
            }

            val sdf1 = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
            val startDateTimeMain: Date = sdf1.parse(startDate)!!
            val currentDateTimeMain: Date = sdf1.parse(currentDate)!!
            if (currentDateTimeMain.after(startDateTimeMain) || currentDateTimeMain == startDateTimeMain) {
                val intent = Intent(
                    context,
                    LockDetailsActivity::class.java
                ).putExtra("lockDetails", locksModel)
                startActivity(intent)
            } else {
                defaultDialog(
                    requireActivity(),
                    getString(keyless.feature.common.R.string.this_lock_will_be_accessible_on) + " \n" + dateToDisplay,
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            }
        } else {
            val intent = Intent(
                context,
                LockDetailsActivity::class.java
            ).putExtra("lockDetails", locksModel)
            startActivity(intent)
        }
    }
}