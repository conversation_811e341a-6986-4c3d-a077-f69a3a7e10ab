<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.authentication.login.ForceUpdatePasswordFragment">


    <TextView
        android:id="@+id/textViewHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/change_password"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/current_password"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />


    <EditText
        android:id="@+id/etCurrentPassword"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/current_password"
        android:inputType="textPassword"
        android:padding="14dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView2" />


    <ImageView
        android:id="@+id/showPassCurrent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_eye_hide"
        app:layout_constraintBottom_toBottomOf="@+id/etCurrentPassword"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etCurrentPassword" />

    <TextView
        android:id="@+id/forgotPassword"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/forgot_password_q"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etCurrentPassword" />

    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/new_password"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forgotPassword" />

    <EditText
        android:id="@+id/etNewPassword"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/password"
        android:inputType="textPassword"
        android:padding="14dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView3" />

    <ImageView
        android:id="@+id/showPassNew"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_eye_hide"
        app:layout_constraintBottom_toBottomOf="@+id/etNewPassword"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etNewPassword" />


    <CheckBox
        android:id="@+id/check1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:button="@drawable/checkbox"
        android:checked="false"
        android:clickable="false"
        android:focusable="false"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        android:paddingStart="6dp"
        android:text="@string/at_least_8_characters"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etNewPassword" />

    <CheckBox
        android:id="@+id/check2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:button="@drawable/checkbox"
        android:checked="false"
        android:clickable="false"
        android:focusable="false"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        android:paddingStart="6dp"
        android:text="@string/containing_a_letter"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/check1" />

    <CheckBox
        android:id="@+id/check3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:button="@drawable/checkbox"
        android:checked="false"
        android:clickable="false"
        android:focusable="false"
        android:fontFamily="@font/poppins_regular_400"
        android:includeFontPadding="false"
        android:paddingStart="6dp"
        android:text="@string/a_number"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/check2" />


    <TextView
        android:id="@+id/textView4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/confirm_new_password"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/check3" />


    <EditText
        android:id="@+id/etConfirmPassword"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:background="@drawable/bg_edit_corners"
        android:fontFamily="@font/poppins_medium_500"
        android:hint="@string/password"
        android:inputType="textPassword"
        android:padding="14dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView4" />

    <ImageView
        android:id="@+id/showPassConfirmNew"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_eye_hide"
        app:layout_constraintBottom_toBottomOf="@+id/etConfirmPassword"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etConfirmPassword" />


    <TextView
        android:id="@+id/btnSubmit"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/submit"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>