<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="feature.authentication.login.CreateNewPasswordFragment">

    <TextView
        android:id="@+id/textViewHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/create_new_password"
        android:textColor="@color/black"
        android:textSize="22dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <TextView
        android:id="@+id/textView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/new_password"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewHeading" />

    <EditText
        android:id="@+id/tvPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_corners"
        android:hint="@string/password"
        android:layout_marginTop="4dp"
        android:inputType="textPassword"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_medium_500"
        android:padding="14dp"
        style="@style/mirrorText"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView3" />

      <TextView
        android:id="@+id/textView4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/confirm_new_password"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPassword" />

    <EditText
        android:id="@+id/tvConfirmPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_corners"
        android:hint="@string/password"
        style="@style/mirrorText"
        android:layout_marginTop="4dp"
        android:inputType="textPassword"
        android:textSize="16dp"
        android:fontFamily="@font/poppins_medium_500"
        android:padding="14dp"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView4" />

    <TextView
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="60dp"
        android:layout_marginEnd="60dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/bg_btn_round"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/submit"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>