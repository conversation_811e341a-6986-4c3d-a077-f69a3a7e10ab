<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/maintenance_nav_graph"
        app:startDestination="@id/maintenanceHomeFragment">

    <fragment
            android:id="@+id/maintenanceHomeFragment"
            android:name="feature.settings.maintenance.home.MaintenanceHomeFragment"
            android:label="MaintenanceHomeFragment" >
        <action
                android:id="@+id/action_maintenanceHome_to_verifyNearByLock"
                app:destination="@id/verifyNearByLockActivity" />
    </fragment>
    <activity
            android:id="@+id/verifyNearByLockActivity"
            android:name="feature.settings.maintenance.scan.VerifyNearByLockActivity"
            android:label="VerifyNearByLockActivity" />
</navigation>