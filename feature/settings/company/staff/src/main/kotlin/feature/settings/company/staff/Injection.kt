package feature.settings.company.staff

import domain.settings.company.staff.add.injection.AddStaffDomainScope
import domain.settings.company.staff.manage.injection.ManageStaffDomainScope
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.component.getScopeId
import org.koin.dsl.module

val manageStaffFeatureModule = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<ManageStaffDomainScope>(ManageStaffDomainScope.getScopeId())
        ManageStaffViewModel(
            viewModel = scope.get(),
            onClear = { scope.close() },
            handler = get()
        )
    }
}

val addStaffFeatureModule = module {
    viewModel {
        val scope = getKoin().getOrCreateScope<AddStaffDomainScope>(AddStaffDomainScope.getScopeId())
        AddStaffViewModel(
            viewModel = scope.get(),
            onClear = { scope.close() },
            handler = get()
        )
    }
}