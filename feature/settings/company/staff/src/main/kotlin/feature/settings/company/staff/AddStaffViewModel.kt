package feature.settings.company.staff

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import domain.common.ErrorHandler
import domain.settings.company.staff.add.models.Event
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class AddStaffViewModel(
    private val viewModel: domain.settings.company.staff.add.ViewModel,
    private val handler: <PERSON>rrorHandler,
    private val onClear: () -> Unit,
    coroutineContext: CoroutineContext? = null
) : ViewModel() {

    private val scope = if (coroutineContext != null) viewModelScope + coroutineContext else viewModelScope

    val screen = viewModel.stateScreenStream
    val sideEffects = viewModel.sideEffectsStream

    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }

//
//    fun updateStaff(
//        context: Context,
//        staffID: String,
//        countryCode: String,
//        mobile: String,
//        email: String,
//        username: String,
//        firstName: String,
//        lastName: String,
//        role: String,
//        passport: String,
//        callbacks: (Boolean) -> Unit
//    ) {
//        val token = SharedPreferenceUtils.getInstance(context).token
//        viewModelScope.launch(
//            CoroutineExceptionHandler { coroutineContext, throwable ->
//                ProgressDialogUtils.getInstance().hideProgress()
//                context.toast(throwable.message!!)
//            }
//        ) {
//            ProgressDialogUtils.getInstance().showProgress(context, false)
//            val staffModel = ApiUtils.servicesInterface.updateStaff(
//                token,
//                countryCode,
//                mobile,
//                email,
//                username,
//                firstName,
//                lastName,
//                role,
//                passport,
//                staffID
//            )
//            ProgressDialogUtils.getInstance().hideProgress()
//            staffModel.message?.let {
//                context.toast(it)
//            }
//            if (staffModel.success == true) {
//                callbacks(true)
//            }
//        }
//    }
}