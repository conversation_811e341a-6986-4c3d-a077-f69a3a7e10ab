package feature.settings.support.nearby

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import domain.settings.support.models.NearbyLock
import keyless.feature.common.databinding.ItemLockListBinding

class NearByLockAdapter(
    private val context: Context
) : RecyclerView.Adapter<NearByLockAdapter.LockViewHolder>() {

    private val items: MutableList<NearbyLock> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, position: Int): LockViewHolder {
        val binding = ItemLockListBinding.inflate(LayoutInflater.from(context), parent, false)
        return LockViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return items.size
    }

    inner class LockViewHolder(val binding: ItemLockListBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(i: NearbyLock, position: Int) {
            binding.txtLockName.isVisible = i.isAvailable
            binding.txtLockAddress.isVisible = i.isAvailable
            binding.txtLockName.text = "Lock Name: " + i.name
            binding.txtLockAddress.text = i.address
            binding.txtLockModel.text = i.model
            binding.txtAccess.text = if (i.isAvailable) "Access available" else "Access not available"
            binding.txtAccess.setTextColor(
                if (i.isAvailable) {
                    context.getColor(keyless.feature.common.R.color.green_positive)
                } else {
                    context.getColor(keyless.feature.common.R.color.red_negative)
                }
            )
            binding.txtLockBrand.text = "Brand: ${i.brand}"
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: LockViewHolder, position: Int) {
        val i = items[position]
        viewHolder.bind(i, position)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clean() {
        items.removeAll(items)
        notifyDataSetChanged()
    }

    fun addNewItems(locks: List<NearbyLock>) {
        if (locks.isEmpty()) {
            clean()
            return
        }

        val newLocks = locks.filter { lock -> items.find { item -> lock.name == item.name } == null }

        for (lock in newLocks) {
            this.items.add(lock)
            notifyItemRangeInserted(items.size, 1)
        }
    }
}