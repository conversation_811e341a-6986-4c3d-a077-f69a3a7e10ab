package feature.settings.checkin

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import feature.common.application.App.REGULA_SCANNER_ACTIVITY_CLASS
import feature.regula.refactored.RegulaActivity
import keyless.feature.settings.checkin.databinding.ActivityCheckInStartBinding

class CheckInStartActivity : AppCompatActivity() {

    lateinit var binding: ActivityCheckInStartBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCheckInStartBinding.inflate(layoutInflater)
        setContentView(binding.root)
        clickListeners()
    }

    private fun clickListeners() {
        val bookingNumber = intent.getStringExtra("bookingNumber")
        binding.btnNext.setOnClickListener {
            if (REGULA_SCANNER_ACTIVITY_CLASS == null) return@setOnClickListener

            startActivityForResult(
                Intent(this, RegulaActivity::class.java)
                    .putExtra("bookingNumber", bookingNumber), 50
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 80) {
            setResult(90)
            finish()
        }
    }
}