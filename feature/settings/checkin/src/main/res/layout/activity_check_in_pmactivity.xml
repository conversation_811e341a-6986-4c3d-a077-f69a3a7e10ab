<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    tools:context="feature.settings.checkin.CheckInPMActivity">

    <ImageView
        android:id="@+id/imageView12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        style="@style/ImageMirror"
        android:src="@drawable/iv_back_white"
        app:layout_constraintBottom_toBottomOf="@+id/moreTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/moreTitle" />

    <View
        android:id="@+id/viewBack"
        android:layout_width="40dp"
        android:layout_marginStart="20dp"
        android:layout_height="40dp"
        app:layout_constraintBottom_toTopOf="@+id/constraintLayout5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/moreTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/poppins_medium_500"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/check_in_"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout5"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/moreTitle">

        <EditText
            android:id="@+id/svCheckIn"
            style="@style/mirrorText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/bg_edit_grey"
            android:drawableStart="@drawable/ic_baseline_search_24"
            android:drawablePadding="10dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_regular_400"
            android:hint="@string/search_by_name_and_booking_number"
            android:imeOptions="actionDone"
            android:includeFontPadding="false"
            android:padding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingRight="30dp"
            android:singleLine="true"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/stopSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:src="@drawable/iv_cross_black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/svCheckIn"
            app:layout_constraintEnd_toEndOf="@+id/svCheckIn"
            app:layout_constraintTop_toTopOf="@+id/svCheckIn" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCheckIn"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/svCheckIn"
            app:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/noStaffMember"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/iv_no_staff_member_found"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:text="@string/no_guest_found"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/constraintLayout5" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressBar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>