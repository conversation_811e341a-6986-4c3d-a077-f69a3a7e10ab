<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/app"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:id="@+id/mainLay"
    android:theme="@style/Theme.MaterialComponents.Light"
    card_view:cardBackgroundColor="@color/card_bg_color"
    card_view:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    card_view:cardMaxElevation="0dp"
    card_view:cardUseCompatPadding="true"
    card_view:strokeColor="@color/card_bg_color"
    card_view:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey_round">


        <TextView
            android:id="@+id/txtInternalId"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="16dp"
            style="@style/mirrorText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            card_view:layout_constraintEnd_toEndOf="parent"
            card_view:layout_constraintHorizontal_bias="0.0"
            card_view:layout_constraintStart_toStartOf="parent"
            card_view:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/txtCardId"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            style="@style/mirrorText"
            android:textSize="14dp"
            card_view:layout_constraintEnd_toEndOf="@+id/txtInternalId"
            card_view:layout_constraintStart_toStartOf="@+id/txtInternalId"
            card_view:layout_constraintTop_toBottomOf="@+id/txtInternalId" />

        <TextView
            android:id="@+id/txtStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/poppins_regular_400"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="14dp"
            style="@style/mirrorText"
            card_view:layout_constraintBottom_toBottomOf="parent"
            card_view:layout_constraintEnd_toEndOf="@+id/txtCardId"
            card_view:layout_constraintStart_toStartOf="@+id/txtCardId"
            card_view:layout_constraintTop_toBottomOf="@+id/txtCardId"
            card_view:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>