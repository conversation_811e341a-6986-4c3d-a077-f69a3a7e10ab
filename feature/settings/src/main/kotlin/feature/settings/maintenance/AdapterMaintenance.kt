package feature.settings.maintenance

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.RecyclerView
import core.lock.common.models.LockBrand
import feature.dfu.view.nearbyDfuActivity.NearByDfuDeviceActivity
import feature.settings.cards.SelectRayonicsLock
import feature.settings.maintenance.scan.VerifyNearByLockActivity
import feature.settings.maintenance.scan.VerifyNearByLockActivity.Companion.VERIFY_NEARBY_LOCK_UNIQUE_KEY
import feature.settings.maintenance.scan.VerifyNearByLockActivity.Companion.VERIFY_NEARBY_LOCK_UNIQUE_REQUEST_CODE
import keyless.feature.settings.databinding.LayMaintenanceBinding

class AdapterMaintenance(
    var listMaintenance: List<ModelMaintenance>
) : RecyclerView.Adapter<AdapterMaintenance.ViewHolder>() {

    lateinit var context: Context

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context
        val binding = LayMaintenanceBinding.inflate(LayoutInflater.from(context), viewGroup, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(listMaintenance[position])
    }

    override fun getItemCount() = listMaintenance.size
    inner class ViewHolder(val binding: LayMaintenanceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: ModelMaintenance) {
            binding.txtName.text = model.name

            binding.mainLay.setOnClickListener {
                if (model.value == "1") {

                    if (startRefactoredNearby()) {
                        return@setOnClickListener
                    }

                    context.startActivity(
                        Intent(
                            context,
                            SelectRayonicsLock::class.java
                        ).putExtra("maintenance", "maintenance")
                    )
                } else if (model.value == "2") {
                    context.startActivity(
                        Intent(context, NearByDfuDeviceActivity::class.java).putExtra(
                            "installer",
                            "0"
                        )
                    )
                }
            }
        }
    }

    private fun startRefactoredNearby(): Boolean {
        if (context !is AppCompatActivity) return false
        val brands = LockBrand
            .values()
            .filter { it != LockBrand.Messerschmitt }

        (context as AppCompatActivity).startActivityForResult(
            Intent(context, VerifyNearByLockActivity::class.java)
                .putExtra("brands", brands.map { it.toString() }.toTypedArray()),
            VERIFY_NEARBY_LOCK_UNIQUE_REQUEST_CODE
        )

        return true
    }
}