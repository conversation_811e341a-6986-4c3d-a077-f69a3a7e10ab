package feature.settings.profile

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import domain.common.ErrorHandler
import domain.settings.profile.models.Event
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext

class ChangeEmailViewModel(
    private val viewModel: domain.settings.profile.ViewModel,
    private val onClear: () -> Unit,
    private val handler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    coroutineContext: CoroutineContext? = null
): ViewModel() {

    private val scope = if (coroutineContext != null) CoroutineScope(coroutineContext) else viewModelScope
    val screenStream = viewModel.screenStream
    val sideEffects = viewModel.sideEffectsStream

    fun onEvent(event: Event) {
        scope.launch { handler.async { viewModel.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}