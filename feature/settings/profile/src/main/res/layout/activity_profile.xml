<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent"
    android:id="@+id/profileParent"
    tools:context=".dashboard.moreSettings.profile.my.ProfileActivity">

    <ImageView
        android:id="@+id/backBtnProfile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="20dp"
        style="@style/ImageMirror"
        android:layout_marginTop="10dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/propertyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/myprofile"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtnProfile"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtnProfile" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtnProfile"
        android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="30dp">


            <TextView
                android:id="@+id/textView15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/firstname"
                android:layout_marginTop="25dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/firstNameET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:hint="@string/firstname"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView15"
                app:layout_constraintTop_toBottomOf="@+id/textView15" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/last_name"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/firstNameET" />

            <EditText
                android:id="@+id/lastNameET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:padding="14dp"
                android:layout_marginTop="4dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:textColor="@color/black"
                android:hint="@string/last_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView16"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView17"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/user_name"
                android:textColor="@color/disable_color"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lastNameET" />

            <EditText
                android:enabled="false"
                android:id="@+id/userNameET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="4dp"
                android:cursorVisible="false"
                android:clickable="true"
                android:focusable="false"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                android:inputType="text|textCapWords"
                android:hint="@string/user_name"
                android:backgroundTint="@color/disable_color"
                android:textColor="@color/disable_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView17"
                app:layout_constraintTop_toBottomOf="@+id/textView17" />

            <TextView
                android:layout_marginTop="15dp"
                android:id="@+id/textView18"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/email"
                android:textColor="@color/disable_color"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/userNameET" />

            <EditText
                android:id="@+id/emailProfileET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:cursorVisible="false"
                android:layout_marginTop="4dp"
                android:clickable="true"
                android:focusable="false"
                android:hint="@string/email"
                android:inputType="textEmailAddress"
                android:backgroundTint="@color/disable_color"
                android:textColor="@color/disable_color"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:background="@drawable/bg_edit_corners"
                android:fontFamily="@font/poppins_medium_500"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/textView18"
                app:layout_constraintTop_toBottomOf="@+id/textView18" />

            <TextView
                android:id="@+id/changeEmailBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/poppins_medium_500"
                android:includeFontPadding="false"
                android:padding="4dp"
                android:text="@string/update"
                android:textColor="@color/blue"
                app:layout_constraintBottom_toBottomOf="@+id/textView18"
                app:layout_constraintEnd_toEndOf="@+id/emailProfileET"
                app:layout_constraintTop_toTopOf="@+id/textView18" />

            <TextView
                android:id="@+id/textView19"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/mobile_number"
                android:textColor="@color/disable_color"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/emailProfileET" />

<!--            <EditText-->
<!--                app:layout_constraintWidth_percent="0.2"-->
<!--                app:layout_constraintBottom_toBottomOf="@+id/tvMobileNumber"-->
<!--                app:layout_constraintEnd_toStartOf="@+id/tvMobileNumber"-->
<!--                android:id="@+id/countryCodeET"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="0dp"-->
<!--                android:backgroundTint="@color/disable_color"-->
<!--                android:clickable="true"-->
<!--                android:padding="14dp"-->
<!--                style="@style/mirrorText"-->
<!--                android:background="@drawable/bg_edit_corners"-->
<!--                android:fontFamily="@font/poppins_medium_500"-->
<!--                android:textSize="16dp"-->
<!--                android:includeFontPadding="false"-->
<!--                android:textColor="@color/disable_color"-->
<!--                android:cursorVisible="false"-->
<!--                android:focusable="false"-->
<!--                android:hint="-"-->
<!--                android:gravity="center"-->
<!--                android:inputType="number">-->
<!--            </EditText>-->

            <com.mikhaellopez.circularimageview.CircularImageView
                android:id="@+id/iv_flag_img"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:clickable="true"
                android:focusable="false"
                app:civ_border="false"
                app:layout_constraintStart_toStartOf="@+id/textView19"
                app:layout_constraintTop_toBottomOf="@+id/textView19"
                tools:src="@drawable/flag_uae" />


            <EditText
                android:id="@+id/tvMobileNumber"
                style="@style/mirrorText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_edit_corners"
                android:backgroundTint="@color/disable_color"
                android:clickable="true"
                android:cursorVisible="false"
                android:enabled="false"
                android:focusable="false"
                android:fontFamily="@font/poppins_medium_500"
                android:hint="@string/enter_mobile_number"
                android:includeFontPadding="false"
                android:inputType="number"
                android:padding="14dp"
                android:textColor="@color/disable_color"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="@+id/emailProfileET"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/iv_flag_img"
                app:layout_constraintTop_toBottomOf="@+id/textView19" />

            <TextView
                android:id="@+id/textView20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/password"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMobileNumber" />

            <EditText
                android:id="@+id/passET"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_edit_corners"
                android:enabled="false"
                android:includeFontPadding="false"
                android:padding="14dp"
                style="@style/mirrorText"
                android:fontFamily="@font/poppins_medium_500"
                android:layout_marginTop="4dp"
                android:textSize="16dp"
                android:inputType="text"
                android:textColor="@color/black"
                android:text="@string/pass_hint"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="@+id/textView20"
                app:layout_constraintTop_toBottomOf="@+id/textView20" />

            <TextView
                android:id="@+id/changePassBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/poppins_medium_500"
                android:includeFontPadding="false"
                android:padding="4dp"
                android:text="@string/update"
                android:textColor="@color/blue"
                app:layout_constraintBottom_toBottomOf="@+id/textView20"
                app:layout_constraintEnd_toEndOf="@+id/passET"
                app:layout_constraintTop_toTopOf="@+id/textView20" />

            <TextView
                style="@style/buttonStyle"
                android:layout_marginBottom="60dp"
                android:id="@+id/updateProfileBtn"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="50dp"
                android:text="@string/update"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/passET"
                app:layout_constraintWidth_percent="0.6" />

            <TextView
                android:visibility="gone"
                android:id="@+id/deleteAccountBtn"
                android:layout_marginTop="15dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/updateProfileBtn"
                android:textColor="@color/red2"
                android:fontFamily="@font/poppins_semibold_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/delete_account"/>


        </androidx.constraintlayout.widget.ConstraintLayout>



    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/noInternetLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawableTop="@drawable/no_internet"
        android:drawablePadding="15dp"
        android:fontFamily="@font/poppins_medium_500"
        android:src="@drawable/common_google_signin_btn_icon_dark_normal"
        android:text="@string/no_internet_connection"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>