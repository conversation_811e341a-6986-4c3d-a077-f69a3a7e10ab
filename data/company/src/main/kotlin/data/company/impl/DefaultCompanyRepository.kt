package data.company.impl

import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import data.common.http.ServerResponse
import data.common.http.unwrap
import data.common.preferences.Preferences
import data.company.models.CompanyProfile
import data.company.models.Lias
import data.company.models.dtos.CompanyProfileResponseDto
import data.company.models.dtos.LiasResponseDto
import data.company.repositories.CompanyRepository

class DefaultCompanyRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) : CompanyRepository {
    private val remote = DefaultCompanyRemoteRepository(client, logger, baseUrl)

    override suspend fun getLias(): List<Lias> {
        return remote
            .getLias(
                Preferences.authenticationToken.get()
            )
            .unwrap<LiasResponseDto>()
            .lias
            .map { it.toModel() }
    }

    override suspend fun getCompanyProfile(): CompanyProfile {
        return remote
            .getCompanyProfile(
                Preferences.authenticationToken.get()
            )
            .unwrap<CompanyProfileResponseDto>()
            .data
            .toModel()
    }

    override suspend fun updateCompanyProfile(
        companyName: String,
        address: String,
        country: String,
        city: String,
        trnNumber: String,
        zipCode: String,
        checkIn: Boolean,
        businessType: String,
        businessLia: String,
        tradeLicenseNumber: String,
        timezoneOffset: String,
        timezoneName: String
    ): String {
        return remote
            .updateCompanyProfile(
                companyName = companyName,
                address = address,
                country = country,
                city = city,
                trnNumber = trnNumber,
                zipCode = zipCode,
                checkIn = checkIn,
                businessType = businessType,
                businessLia = businessLia,
                tradeLicenseNumber = tradeLicenseNumber,
                timezoneOffset = timezoneOffset,
                timezoneName = timezoneName,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<ServerResponse>()
            .message
    }
}