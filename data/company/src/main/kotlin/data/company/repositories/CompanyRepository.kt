package data.company.repositories

import data.company.models.CompanyProfile
import data.company.models.Lias

interface CompanyRepository {

    suspend fun getLias(): List<Lias>

    suspend fun getCompanyProfile(): CompanyProfile

    suspend fun updateCompanyProfile(
        companyName: String,
        address: String,
        country: String,
        city: String,
        trnNumber: String,
        zipCode: String,
        checkIn: Boolean,
        businessType: String,
        businessLia: String,
        tradeLicenseNumber: String,
        timezoneOffset: String,
        timezoneName: String
    ): String
}