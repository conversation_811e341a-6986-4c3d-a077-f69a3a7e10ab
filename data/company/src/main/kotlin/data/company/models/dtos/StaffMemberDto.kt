package data.company.models.dtos

import data.company.models.StaffMember
import data.company.models.StaffMemberRole
import kotlinx.serialization.Serializable

@Serializable
internal data class StaffMemberRolesResponseDto(
    val roles: List<StaffMemberRoleResponseDto>
) {
    fun toModel(): List<StaffMemberRole> {
        return roles.map { it.role.toModel() }
    }
}

@Serializable
internal data class StaffMemberRoleResponseDto(
    val role: StaffMemberRoleDto
)

@Serializable
data class StaffMemberRoleDto(
    val _id: String,
    val name: String,
    val display_name: String
) {
    fun toModel(): StaffMemberRole {
        return StaffMemberRole(
            id = _id,
            name = name,
            displayName = display_name
        )
    }
}

@Serializable
data class StaffMembersResponseDto(
    val members: List<StaffMemberDto>
)

@Serializable
data class StaffMemberDto(
    val _id: String,
    val username: String,
    val status: Long,
    val first_name: String,
    val last_name: String,
    val passport_number: String? = "",
    val mobile_number: String = "",
    val country_code: String = "+91",
    val email: String,
    val is_valid: Int,
    val role_data: StaffMemberRoleDto
) {
    fun toModel(): StaffMember {
        return StaffMember(
            id = _id,
            username = username,
            status = status,
            passportNumber = passport_number ?: "",
            mobileNumber = mobile_number,
            countryCode = country_code,
            email = email,
            isValid = is_valid,
            firstName = first_name,
            lastName = last_name,
            role = role_data.toModel()
        )
    }
}