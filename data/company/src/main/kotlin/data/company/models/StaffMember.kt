package data.company.models

data class StaffMemberRole(
    val id: String,
    val name: String,
    val displayName: String
)

data class StaffMember(
    val id: String,
    val username: String,
    val status: Long,
    val firstName: String,
    val lastName: String,
    val passportNumber: String,
    val mobileNumber: String,
    val countryCode: String,
    val email: String,
    val isValid: Int,
    val role: StaffMemberRole
)