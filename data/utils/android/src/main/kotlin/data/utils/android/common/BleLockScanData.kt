package data.utils.android.common

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class BleLockScanData(var bleName: String, var bleMac: String, var scanRecord: ByteArray) :
    Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.createByteArray()!!
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(bleName)
        parcel.writeString(bleMac)
        parcel.writeByteArray(scanRecord)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<BleLockScanData> {
        override fun createFromParcel(parcel: Parcel): BleLockScanData {
            return BleLockScanData(parcel)
        }

        override fun newArray(size: Int): Array<BleLockScanData?> {
            return arrayOfNulls(size)
        }
    }
}