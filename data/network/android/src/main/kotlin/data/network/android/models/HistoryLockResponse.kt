package data.network.android.models

import androidx.annotation.Keep

@Keep
data class HistoryLockResponse(
    var message: String?,
    var whichScreen: Int?,
    var total_count: Int?,
    var accessLogs: ArrayList<AccessLogs>?,
    var success: Boolean?
)

@Keep
class AccessLogs {
    var id: String = ""
    var user_name: String = ""
    var usernameShareBy: String = ""
    var profile_photo: String = ""
    var user_id: String = ""
    var status: String = ""
    var date: String = ""
    var time: String = ""
    var device_model: String = ""
    var mobile_id: String = ""
    var created_at: String = ""
    var valid_from: String = ""
    var valid_to: String = ""
    var share_by: String = ""
    var share_to: String = ""
    var assignmentStatus: Int = 0
    var share_status: Int = 0
    var revokeStatus: Boolean = false
    var revokeBy: String = ""
    var revokeOn: String = ""
    var isSystemRevoked: Boolean = false
}