package data.lock.common.lock.repositories

import data.lock.common.lock.models.LockDetails
import data.lock.common.lock.models.PlatformDetails

interface LocksRepository {

    suspend fun upgradeMaintenance(lockId: String, authentication: String)

    suspend fun downgradeMaintenance(lockId: String, authentication: String)

    suspend fun getDetails(lockName: String, uid: String, authentication: String): LockDetails

    suspend fun deleteLock(lockId: String, authentication: String)

    suspend fun getPlatformDetails(authentication: String): PlatformDetails
}