package data.lock.common.lock.usecases.lock

import core.monitoring.common.repository.Logger
import data.lock.common.lock.repositories.LocksRemote

internal class LockUseCases(
    remote: LocksRemote,
    logger: Logger
) {

    val getDetails = GetLockDetailsUseCase(
        remote = remote,
        logger = logger
    )

    val delete = DeleteLockUseCase(
        remote = remote,
        logger = logger
    )

    val platformDetails = PlatformDetailsUseCase(
        remote = remote,
        logger = logger
    )
}