package data.common.preferences

import core.caching.KeyValueCache
import core.locks.logs.repostiories.LockLogsRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.Qualifier
import org.koin.core.qualifier.named

object Injection : KoinComponent {
    inline fun <reified T : Any> provide(qualifier: Qualifier? = null) = inject<T>(qualifier = qualifier)
}

object Preferences {
    private val settings by Injection.provide<KeyValueCache>(named("regular"))
    private val oldSettings by Injection.provide<KeyValueCache>(named("old"))
    private val persistent by Injection.provide<KeyValueCache>(named("persistent"))
    private var adminTempToken: String = ""
    private var adminTempRole: String = ""
    private var adminTempUserId: String = ""
    private var adminTempUserType: String = ""
    private var isAdminLogin: Boolean = false
    private lateinit var deviceType: String

    val lockLogger by Injection.provide<LockLogsRepository>()

    val isLoggedIn = object : SettingsProperty<Boolean>("isLoggedIn") {
        override fun get() = settings.getBoolean(key, oldSettings.getBoolean(key, false))
        override fun set(value: Boolean) = settings.setBoolean(key, value)
        override fun remove() = settings.remove(key)
    }

    val isLoggedInAnotherDevice = object : SettingsProperty<Boolean>("isLoggedInAnotherDevice") {
        override fun get() = settings.getBoolean(key, oldSettings.getBoolean(key, false))
        override fun set(value: Boolean) = settings.setBoolean(key, value)
        override fun remove() = settings.remove(key)
    }

    val authenticationToken = object : SettingsProperty<String>("token") {
        override fun get() = if (isAdminLogin()) {
            getAdminTempToken()
        } else {
            settings.getString(key, oldSettings.getString("accessToken", ""))
        }

        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val userId = object : SettingsProperty<String>("userId") {
        override fun get(): String {
            return if (isAdminLogin()) getAdminTempUserId() else settings.getString(key, oldSettings.getString(key, ""))
        }

        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val uuid = object : SettingsProperty<String>("uuid") {
        override fun get(): String = settings.getString(key, oldSettings.getString("UUIDKey", ""))
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val firstName = object : SettingsProperty<String>("firstName") {
        override fun get(): String = settings.getString(key, oldSettings.getString(key, ""))
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val userFullName = object : SettingsProperty<String>("userName") {
        override fun get(): String = settings.getString(key, oldSettings.getString(key, ""))
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val name = object : SettingsProperty<String>("name") {
        override fun get(): String = settings.getString(key, oldSettings.getString(key, ""))
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val userRole = object : SettingsProperty<String>("userRole") {
        override fun get() = if (isAdminLogin()) {
            getAdminTempUserType()
        } else {
            settings.getString(key, oldSettings.getString(key, ""))
        }

        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val role = object : SettingsProperty<String>("role") {
        override fun get(): String {
            return if (isAdminLogin()) getAdminTempRole() else settings.getString(key, oldSettings.getString(key, ""))
        }

        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val timeZoneName: PreferencesValue<String> = object : PreferencesValue<String> {
        private var timezoneName: String = "Asia/Dubai"

        override fun get(): String = timezoneName
        override fun set(value: String) {
            timezoneName = value
        }
    }

    val timeZoneOffset: PreferencesValue<String> = object : PreferencesValue<String> {
        private var timeZoneOffset: String = "+04:00"
        override fun get(): String = timeZoneOffset
        override fun set(value: String) {
            timeZoneOffset = value
        }
    }

    val lastOnlineLocksFetchDateTime = object : SettingsProperty<String>("lastOnlineLocksFetchDateTime") {
        override fun get() = persistent.getString(key, "")
        override fun set(value: String) = persistent.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val isPaidUser = object : SettingsProperty<Boolean>("isPaid") {
        override fun get(): Boolean = settings.getBoolean(key, oldSettings.getBoolean(key, false))
        override fun set(value: Boolean) = settings.setBoolean(key, value)
        override fun remove() = settings.remove(key)
    }

    val deviceFCMToken = object : SettingsProperty<String>("deviceToken") {
        override fun get(): String = settings.getString(key, "")
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    val userEmail = object : SettingsProperty<String>("userEmail") {
        override fun get(): String = settings.getString(key, "")
        override fun set(value: String) = settings.setString(key, value)
        override fun remove() = settings.remove(key)
    }

    fun clear() {
        settings.clear()
        oldSettings.clear()
    }

    fun getAdminTempToken(): String {
        return adminTempToken
    }

    fun setAdminTempToken(token: String) {
        adminTempToken = token
    }

    fun getAdminTempRole(): String {
        return adminTempRole
    }

    fun setAdminTempRole(role: String) {
        adminTempRole = role
    }

    fun getAdminTempUserId(): String {
        return adminTempUserId
    }

    fun setAdminTempUserId(userId: String) {
        adminTempUserId = userId
    }

    fun getAdminTempUserType(): String {
        return adminTempUserType
    }

    fun setAdminTempUserType(userType: String) {
        adminTempUserType = userType
    }

    fun isGuest(): Boolean {
        return userRole.get() == UserType.Guest.toString()
    }

    fun isAdminLogin(): Boolean {
        return isAdminLogin
    }

    fun setAdminLogin(isLogin: Boolean) {
        isAdminLogin = isLogin
    }

    fun getDeviceType(): String {
        return deviceType
    }

    fun setDeviceType(type: String) {
        deviceType = type
    }

    inline fun <reified T : Any> get() = runCatching { Injection.provide<T>().value }.getOrNull()
}

interface PreferencesValue<T> {
    fun get(): T
    fun set(value: T)
}

abstract class SettingsProperty<T>(protected val key: String) {
    abstract fun get(): T
    abstract fun set(value: T)
    abstract fun remove()
}
object Roles {
    const val SYSTEM_MANAGER = "System Manager"
    const val CUSTOMER_SERVICES = "Customer Services"
    const val VIEWER_ACCESS = "Viewer access"

    fun isLimitedTimeAccessRoles(role: String): Boolean {
        return listOf(SYSTEM_MANAGER, CUSTOMER_SERVICES, VIEWER_ACCESS).contains(role)
    }
}