package data.common

import data.common.preferences.Preferences
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toJavaLocalDateTime
import kotlinx.datetime.toLocalDateTime

fun Instant.device(): LocalDateTime {
    return toLocalDateTime(TimeZone.currentSystemDefault())
}

fun now(): LocalDateTime {
    return Clock.System.now().device()
}

fun LocalDateTime.isAfter(other: LocalDateTime): Boolean {
    return toJavaLocalDateTime().isAfter(other.toJavaLocalDateTime())
}

fun lastServerTime(): LocalDateTime? {
    return runCatching { Instant.parse(Preferences.lastOnlineLocksFetchDateTime.get()).device() }
        .getOrNull()
}