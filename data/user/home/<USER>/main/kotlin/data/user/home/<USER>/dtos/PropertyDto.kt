package data.user.home.models.dtos

import data.lock.common.lock.models.lock.Property
import data.lock.common.lock.models.lock.PropertyDetails
import kotlinx.serialization.Serializable

@Serializable
internal data class PropertyDto(
    val _id: String,
    val latitude: String = "",
    val longitude: String = "",
    val manager_id: String = "",
    val manager_type: String = "",
    val emirate: String = "",
    val area: String = "",
    val building_name: String,
    val total_floors: Long = 0,
    val created_at: String = "",
    val icon_id: String = "",
    val support_call_number: String = "",
    val support_whatsapp_number: String = "",
    val icon: ArrayList<IconDto> = ArrayList(),
    val count: Int = 0,
    val id: String = _id,
    val laundary_number: String = "",
    val grocery_number: String = "",
    val maintainance_number: String = ""
) {
    fun toModel(): Property {
        return Property(
            latitude = latitude,
            longitude = longitude,
            managerId = manager_id,
            managerType = manager_type,
            emirate = emirate,
            area = area,
            buildingName = building_name,
            totalFloors = total_floors,
            createdAt = created_at,
            iconId = icon_id,
            supportCallNumber = support_call_number,
            supportWhatsappNumber = support_whatsapp_number,
            icons = icon.map { it.toModel() },
            count = count,
            id = id,
            laundryNumber = laundary_number,
            groceryNumber = grocery_number,
            maintenanceNumber = maintainance_number
        )
    }
}

@Serializable
internal data class PropertyDetailsDto(
    val _id: String,
    val latitude: String = "",
    val longitude: String = "",
    val manager_id: String = "",
    val manager_type: String = "",
    val emirate: String = "",
    val area: String = "",
    val building_name: String,
    val total_floors: Long = 0,
    val created_at: String = "",
    val icon_id: String = "",
    val support_call_number: String = "",
    val support_whatsapp_number: String = "",
    val count: Int = 0,
    val id: String = _id,
    val floor: String = "",
    val name: String = "",
    val room_number: String = "",
    val laundary_number: String = "",
    val grocery_number: String = "",
    val maintainance_number: String = "",
    val appartment_number: String = "",
    val map_id: String = ""
) {
    fun toModel(): PropertyDetails {
        return PropertyDetails(
            latitude = latitude,
            longitude = longitude,
            managerId = manager_id,
            managerType = manager_type,
            emirate = emirate,
            area = area,
            buildingName = building_name,
            totalFloors = total_floors,
            createdAt = created_at,
            iconId = icon_id,
            supportCallNumber = support_call_number,
            supportWhatsappNumber = support_whatsapp_number,
            count = count,
            id = id,
            floor = floor,
            name = name,
            roomNumber = room_number,
            laundryNumber = laundary_number,
            groceryNumber = grocery_number,
            maintenanceNumber = maintainance_number,
            apartmentNumber = appartment_number,
            mapId = map_id
        )
    }
}