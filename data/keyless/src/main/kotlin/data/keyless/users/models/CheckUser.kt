package data.keyless.users.models

import android.annotation.SuppressLint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class CheckUserRequest(
    val uid: String,
    @SerialName("is_admin")
    val isAdmin: Boolean
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class CheckUserResponse(
    val success: Boolean = false,
    @SerialName("force_update")
    val forceUpdate: Boolean = false,
    @SerialName("force_password_require")
    val forcePasswordRequire: Boolean = false,
    @SerialName("due_date")
    val dueDate: String = "",
    val timezone: String = "",
    @SerialName("timezone_name")
    val timezoneName: String = "",
    val message: String = ""
)
