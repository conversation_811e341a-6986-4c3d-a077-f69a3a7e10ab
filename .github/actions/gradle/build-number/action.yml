name: "Build number"
description: "Build number"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"

  githubToken:
    required: true
    description: "Github token"
  repository:
    required: true
    description: "Github repository"
  sedVariant:
    required: true
    description: "sed variant to use"

outputs:
  build-number:
    description: "Version number"
    value: ${{ steps.version_code.outputs.code }}
    # value: "240"

runs:
  using: "composite"
  steps:
    - name: Prepare Version Code
      id: version_code
      # Get the total count of the successful app builds distributed using
      # pipelines and adds 1 to them
      # + 112 is the old staging workflow run count number
      run: |
        RC_URL=$(echo "https://api.github.com/repos/${{ inputs.repository }}/actions/workflows/distribute-release-candidate.yml/runs?per_page=1&status=success" | ${{ inputs.sedVariant }} "s/ /%20/g")
        PRODUCTION_URL=$(echo "https://api.github.com/repos/${{ inputs.repository }}/actions/workflows/distribute-production.yml/runs?per_page=1&status=success" | ${{ inputs.sedVariant }} "s/ /%20/g")
        STAGING_URL=$(echo "https://api.github.com/repos/${{ inputs.repository }}/actions/workflows/distribute-staging.yml/runs?per_page=1&status=success" | ${{ inputs.sedVariant }} "s/ /%20/g")
    
        RC_RUN_NUMBER=$(curl -v -H 'Authorization: Bearer ${{ inputs.githubToken }}' -H 'Accept: application/vnd.github.v3+json'   "$RC_URL" 2>&1 | grep total_count | awk '{print $2}' | ${{ inputs.sedVariant }} 's/,//g')
        PROD_RUN_NUMBER=$(curl -v -H 'Authorization: Bearer ${{ inputs.githubToken }}' -H 'Accept: application/vnd.github.v3+json'   "$PRODUCTION_URL" 2>&1 | grep total_count | awk '{print $2}' | ${{ inputs.sedVariant }} 's/,//g')
        STAGING_RUN_NUMBER=$(curl -v -H 'Authorization: Bearer ${{ inputs.githubToken }}' -H 'Accept: application/vnd.github.v3+json'   "$STAGING_URL" 2>&1 | grep total_count | awk '{print $2}' | ${{ inputs.sedVariant }} 's/,//g')
        
        echo "code=$(($RC_RUN_NUMBER + $PROD_RUN_NUMBER + $STAGING_RUN_NUMBER + 1))" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}