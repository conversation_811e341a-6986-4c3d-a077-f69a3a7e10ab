name: "Sign APK"
description: "Sign APK"
inputs:
  releaseDir:
    required: false
    description: "Path to release directory"
    default: "app/build/outputs/apk/release"
  signingKey:
    required: true
    description: "Signing key"
  keyAlias:
    required: true
    description: "Key alias"
  keyStorePassword:
    required: true
    description: "Key store password"
  keyPassword:
    required: true
    description: "Key password"
  buildToolsVersion:
    required: false
    description: "Build tools version"
    default: "33.0.0"

outputs:
    signedApkPath:
        description: "Path to signed APK"
        value: ${{ steps.sign-app.outputs.signedFile }}

runs:
  using: "composite"
  steps:
    - uses: ilharp/sign-android-release@v1.0.4 # Or use @nightly
      name: Sign app APK
      id: sign-app
      with:
        releaseDir: ${{ inputs.releaseDir }}
        signingKey: ${{ inputs.signingKey }}
        keyAlias: ${{ inputs.keyAlias }}
        keyStorePassword: ${{ inputs.keyStorePassword }}
        keyPassword: ${{ inputs.keyPassword }}
        buildToolsVersion: ${{ inputs.buildToolsVersion }}