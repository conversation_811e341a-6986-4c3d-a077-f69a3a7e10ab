name: "Bump Android Version"
description: "Bump Android App Version"
inputs:
  gradlePath:
    required: false
    description: "Path to app module"
    default: "app/build.gradle.kts"
  versionCode:
    required: true
    description: "Version code"
  versionName:
    required: true
    description: "Version name"

runs:
  using: "composite"
  steps:
    - name: Prepare Version Code
      id: version_code
      run: |
             INPUT_VERSION=${{ inputs.versionCode }}
             
             echo "code=$(($INPUT_VERSION + 125))" >> $GITHUB_OUTPUT
      shell: ${{ inputs.shell }}

    - name: Bump version
      uses: chkfung/android-version-actions@v1.2.1
      with:
        gradlePath: ${{ inputs.gradlePath }}
        versionCode: ${{ steps.version_code.outputs.code }}
        versionName: ${{ inputs.versionName }}
