name: "Inject huawei secrets"
description: "Inject huawei secrets"
inputs:
  shell:
    required: true
    description: "Shell to use depending on runner os"
  sedVariant:
    required: true
    description: "Sed variant to use depending on runner os"

  huaweiKeylessClientSecret:
    description: "Huawei keyless client secret"
    required: true
  huaweiKeylessAppId:
    description: "Huawei keyless app id"
    required: true
  huaweiKeylessApiKey:
    description: "Huawei keyless api key"
    required: true

runs:
  using: "composite"
  steps:

    - name: Inject huawei secrets
      run: |
        cp app/agconnect-services.json.templete app/agconnect-services.json
        
        ${{ inputs.sedVariant }} -i 's/_KEYLESS_APP_ID_/${{ inputs.huaweiKeylessAppId }}/g' app/agconnect-services.json
        ${{ inputs.sedVariant }} -i 's/_KEYLESS_API_KEY_/${{ inputs.huaweiKeylessApiKey }}/g' app/agconnect-services.json
        ${{ inputs.sedVariant }} -i 's/_KEYLESS_CLIENT_SECRET_/${{ inputs.huaweiKeylessClientSecret }}/g' app/agconnect-services.json
      shell: ${{ inputs.shell }}