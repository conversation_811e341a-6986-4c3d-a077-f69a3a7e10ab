name: "Download artifact"
description: "Download artifact"
inputs:
  downloadName:
    required: true
    description: "Download name"
  downloadPath:
    required: true
    description: "Download path"

outputs:
  downloadedFile:
    description: "Download path"
    value: ${{ steps.download.outputs.download-path }}

runs:
  using: "composite"
  steps:
    - name: Download artifact
      id: download
      uses: actions/download-artifact@v4
      with:
        path: ${{ inputs.downloadPath }}
        pattern: ${{ inputs.downloadName }}
        merge-multiple: true

    - name: Echo download path
      shell: "bash"
      run: echo ${{steps.download.outputs.download-path}}