concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

name: Distribute appstore

on:
  push:
    tags:
      - "ios.[0-9]+.[0-9]+.[0-9]+"

  workflow_dispatch:

jobs:
  build-ios:
    name: Build release ipa
    uses: ./.github/workflows/reusable-build-ipa.yml
    secrets: inherit
    with:
      artifactUploadIpaName: "built-ipa"
      artifactUploadDSYMName: "built-dsym"
      versionName: ${{ github.ref_name }}

      environment: "production"

  distribute-ios:
    name: Distribute Ios
    uses: ./.github/workflows/reusable-ipa-distribution.yml
    secrets: inherit
    needs: build-ios
    with:
      artifactDownloadIpaName: "built-ipa"
      ipaFullPath: ${{ needs.build-ios.outputs.ipaPath }}/${{ needs.build-ios.outputs.ipaName }}

      environment: "production"

  remove-ios-deployments:
    name: Remove auto ios deployments
    runs-on: ubuntu-22.04
    needs: distribute-ios
    if: always()
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/deployments-delete

  cancel-ios-workflow:
    name: Cancel ios workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [build-ios]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action

  cancel-ios-distribute-workflow:
    name: Cancel ios distribute workflow
    runs-on: ubuntu-22.04
    if: failure()
    needs: [distribute-ios]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Delete deployments
        uses: ./.github/actions/job/cancel-action