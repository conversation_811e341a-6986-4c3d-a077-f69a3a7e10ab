name: Google play distribution

on:
  workflow_call:
    inputs:
      artifactDownloadApkName:
        required: true
        description: "name of artifact upload"
        type: string
      artifactDownloadMappingName:
        required: true
        description: "name of artifact upload"
        type: string
      apkFullPath:
        required: true
        description: "Path to the apk"
        type: string
      mappingFullPath:
        required: true
        description: "Path to the mapping"
        type: string
      track:
        required: true
        description: "Track to distribute to"
        type: string

      environment:
        required: true
        description: "Environment to build for"
        type: string

jobs:
  distribute:
    name: Distribute google play
    runs-on: ubuntu-22.04
    env:
      shell: "bash"
      sed: "sed"
    environment: ${{ inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: "0"

      - name: Relative signed file path
        id: relativeAppPath
        uses: ./.github/actions/job/relative-path
        with:
          fullPath: ${{ inputs.apkFullPath }}
          sedVariant: ${{ env.sed }}
          shell: ${{ env.shell }}

      - name: Relative mapping file path
        id: relativeMappingPath
        uses: ./.github/actions/job/relative-path
        with:
          fullPath: ${{ inputs.mappingFullPath }}
          sedVariant: ${{ env.sed }}
          shell: ${{ env.shell }}

      - name: Download signed apk
        id: apkDownload
        uses: ./.github/actions/job/artifact-download
        with:
          downloadName: ${{ inputs.artifactDownloadApkName }}
          downloadPath: ${{ steps.relativeAppPath.outputs.filePath }}

      - name: Download mapping file
        id: mappingDownload
        uses: ./.github/actions/job/artifact-download
        with:
          downloadName: ${{ inputs.artifactDownloadMappingName }}
          downloadPath: ${{ steps.relativeMappingPath.outputs.filePath }}

      - name: Distribute
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_FILE }}
          packageName: com.keyless_dubai
          releaseFiles: ${{ steps.relativeAppPath.outputs.filePath }}/${{ steps.relativeAppPath.outputs.fileName }}
          track: ${{ inputs.track }}
          status: draft
          inAppUpdatePriority: 5
          mappingFile: ${{ steps.relativeMappingPath.outputs.filePath }}/${{ steps.relativeMappingPath.outputs.fileName }}