package presentation.home.dashboard.feature

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.keyless.home.LockSummary
import keyless.presentation.common.R
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import presentation.common.feature.state.ListState
import presentation.common.feature.state.MutableItemState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder()
)

internal class UIStateHolder(
    val name: StringState = StringState(""),
    val changePassword: MutableItemState<ChangePasswordState> = MutableItemState(ChangePasswordState.None),
    val locks: ListState<LockSummary> = ListState(emptyList())
) {
    @Composable
    fun welcomeText(): String {
        val hour = currentHour()
        val firstName = getFirstName()
        val res = when {
            hour in 0..11 && firstName.isNotBlank() -> R.string.text_good_morning
            hour in 0..11 && firstName.isBlank() -> R.string.good_morning_guest
            hour in 12..16 && firstName.isNotBlank() -> R.string.text_good_afternoon
            hour in 12..16 && firstName.isBlank() -> R.string.good_afternoon_guest
            hour in 17..23 && firstName.isNotBlank() -> R.string.text_good_evening
            hour in 17..23 && firstName.isBlank() -> R.string.good_evening_guest
            else -> R.string.good_evening_guest
        }
        return if (firstName.isNotBlank()) stringResource(res) + " " + firstName else stringResource(res)
    }

    private fun getFirstName() = if (name.get().isNotBlank()) name.get().split(" ").first() else ""
    private fun currentHour(): Int = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).hour
}

internal sealed interface ChangePasswordState {
    object None : ChangePasswordState
    data class Show(val dueDate: String) : ChangePasswordState
}