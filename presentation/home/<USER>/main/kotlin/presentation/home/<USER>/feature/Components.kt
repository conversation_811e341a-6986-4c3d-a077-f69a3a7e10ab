package presentation.home.dashboard.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import data.common.utils.ensureEndsWith
import data.keyless.home.LockDetails
import data.keyless.home.LockSummary
import data.keyless.home.PropertyDetails
import keyless.presentation.common.R
import keyless.presentation.home.ConfigValues
import presentation.common.feature.components.AppCarousel
import presentation.common.feature.components.AppCarouselIndicator
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppFlowRow
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable

@Composable
internal fun BoxScope.HomeTopBar(state: StateHolder) {
    GuestTopBar(state)
}

@Composable
internal fun BoxScope.GuestTopBar(state: StateHolder) {
    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterVertically
    ) {
        AppPageTitleText(
            modifier = Modifier.weight(1f),
            text = state.ui.welcomeText(),
            design = DesignSystem.Text.PageTitle.copy(overflow = TextOverflow.Ellipsis)
        )

        AppIcon(res = R.drawable.iv_notifications)

        AppIcon(res = R.drawable.iv_settings)
    }
}

@Composable
internal fun GuestCarousel(modifier: Modifier = Modifier, state: StateHolder) {
    AppCarousel(
        modifier = modifier,
        count = 3,
        contentPadding = PaddingValues(0.dp),
        gridSpacing = DesignSystem.Padding.screen
    ) {
        when (it) {
            0 -> GuestCarouselItem(
                index = it,
                titleRes = R.string.welcome_to_keyless,
                textRes = R.string.one_application
            )

            1 -> GuestCarouselItem(index = it, titleRes = R.string.share_access, textRes = R.string.keyless_enables)
            2 -> GuestCarouselItem(index = it, titleRes = R.string.quick_services, textRes = R.string.choose_from_a)
        }
    }
}

@Composable
internal fun YourKeysRow(modifier: Modifier = Modifier, state: StateHolder, onClaimKey: () -> Unit) {
    AppRow(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.CenterVertically
    ) {
        AppLabelText(
            text = stringResource(R.string.your_keys),
            design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
        )

        AppLabelText(
            modifier = Modifier
                .clip(CircleShape)
                .border(1.dp, MaterialTheme.colorScheme.primary, CircleShape)
                .padding(horizontal = DesignSystem.Padding.medium, vertical = DesignSystem.Padding.small)
                .clickable(onClick = onClaimKey),
            text = stringResource(R.string.claim_your_key),
            design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.primary, weight = FontWeight.Bold)
        )
    }
}

@Composable
internal fun ChangePasswordRow(
    modifier: Modifier = Modifier,
    state: ChangePasswordState.Show,
    onChangePassword: () -> Unit
) {
    AppSurface(
        modifier = modifier.hiddenClickable(onClick = onChangePassword),
        paddings = PaddingValues(horizontal = DesignSystem.Padding.large, vertical = DesignSystem.Padding.medium),
        color = MaterialTheme.colorScheme.errorContainer
    ) {
        AppLabelText(
            text = stringResource(R.string.tap_to_update_your_password_before, state.dueDate),
            design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.error, alignment = TextAlign.Center)
        )
    }
}

@Composable
internal fun LocksSection(modifier: Modifier = Modifier, state: StateHolder) {
    AppFlowRow(
        modifier = modifier,
        maxItemsInEachRow = 2,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.screen),
        alignment = Arrangement.spacedBy(DesignSystem.Padding.screen)
    ) {
        for (summary in state.ui.locks.value) LockItem(modifier = Modifier.weight(1f), lock = summary)

        if (state.ui.locks.value.size % 2 != 0) Box(modifier = Modifier.weight(1f))
    }
}

@Composable
private fun LockItem(modifier: Modifier = Modifier, lock: LockSummary) {
    AppSurface(
        modifier = modifier.border(1.dp, MaterialTheme.colorScheme.outlineVariant, MaterialTheme.shapes.medium),
        paddings = PaddingValues(0.dp),
        color = Color.Transparent,
        shape = MaterialTheme.shapes.medium
    ) {
        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(DesignSystem.Padding.screen),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
            alignment = Alignment.CenterHorizontally
        ) {
            LockItemIcon(lock.lock)

            AppLabelText(
                text = lock.lock.name,
                design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, weight = FontWeight.Bold)
            )

            LockItemPlace(lock.propertyDetails)

            AppLabelText(
                text = lock.propertyDetails.name, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center)
            )
        }

        if (!lock.isLockActiveForUser) LockItemActive(lock.lock)
    }
}

@Composable
private fun LockItemPlace(property: PropertyDetails) {
    val context = LocalContext.current
    val place = remember(property) {
        val comma = context.getString(R.string.comma)
        if (property.appartmentNumber.isEmpty()) property.floor + " " + context.getString(R.string.floor)
        else property.appartmentNumber + "$comma " + property.floor + " " + context.getString(R.string.floor)
    }

    AppLabelText(text = place, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center))
}

@Composable
private fun LockItemActive(lock: LockDetails) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .rotate(-45f),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .width(40.dp)
                .height(12.dp)
                .scale(scaleX = 2f, scaleY = 1f)
                .background(MaterialTheme.colorScheme.primary)
        )

        AppLabelText(
            text = stringResource(R.string.inactive),
            design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.onPrimary, size = 8.sp)
        )
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
private fun LockItemIcon(lock: LockDetails) {
    val icon = remember { lock.icon.firstOrNull()?.icon }
    if (icon?.isNotBlank() == true) {
        GlideImage(
            model = ConfigValues.imageUrl.ensureEndsWith("/") + icon,
            contentDescription = null,
            modifier = Modifier.size(DesignSystem.Icon.medium)
        )
    } else {
        AppIcon(
            modifier = Modifier.size(DesignSystem.Icon.medium),
            res = R.drawable.iv_other_icon,
            tint = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
private fun GuestCarouselItem(
    modifier: Modifier = Modifier,
    index: Int,
    titleRes: Int,
    textRes: Int
) {
    AppSurface(
        modifier = modifier, paddings = PaddingValues(DesignSystem.Padding.screen),
        color = MaterialTheme.colorScheme.tertiary
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.large),
            alignment = Alignment.CenterHorizontally
        ) {
            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
                alignment = Alignment.CenterVertically
            ) {
                AppImage(res = R.drawable.keyless_logo_2)

                AppColumn(
                    modifier = Modifier.weight(1f),
                    arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
                    alignment = Alignment.CenterHorizontally
                ) {
                    AppPageTitleText(text = stringResource(titleRes))
                    AppLabelText(
                        text = stringResource(textRes),
                        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, minLines = 3)
                    )
                }
            }

            AppCarouselIndicator(count = 3, currentPage = index, color = MaterialTheme.colorScheme.primary)
        }
    }
}