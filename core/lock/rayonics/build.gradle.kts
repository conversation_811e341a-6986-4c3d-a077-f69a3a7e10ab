plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
    // id("ios.target.library")
}


android {
    sourceSets {
        getByName("main") {
            jniLibs.srcDir("libs")
        }
    }
}

commonDependencies {
    implementation(project(":core-common"))
    implementation(project(":core-monitoring-common"))
    api(project(":core-http-download-common"))
    api(project(":core-lock-common"))
}

androidDependencies {
    api(project(":core-lock-nordicfirmware"))

    implementation("org.bouncycastle:bcprov-jdk15on:1.59")

    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))
}