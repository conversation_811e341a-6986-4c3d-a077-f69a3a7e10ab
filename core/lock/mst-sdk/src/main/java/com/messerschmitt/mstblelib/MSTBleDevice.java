package com.messerschmitt.mstblelib;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Base64;

import androidx.annotation.RequiresApi;

/**
 * Method Class that gives more functionality for the given gatt
 *
 * <AUTHOR> Systems AG
 * @version 2015.1001
 */
public class MSTBleDevice implements Parcelable {
    private static final String TAG = "SMARTKEY";
    private final BluetoothDevice mDevice;
    private static boolean mIsLrcCorrect;
    private static int mDeviceSystemCode;
    private static int mDeviceNumber;
    private static int mDeviceType;
    private static boolean mDevicePowerFlag;
    /*private int mDeviceSpecialArea;
    private int mFirstRssi;
    private final byte[] mScanRecord;
    */

    public byte AuthMode;
    public byte AppType;
    public byte AppVerHL;
    public byte AppBleHL;
    public byte AppFlags;
    public byte[] DevMAC;
    public byte[] DevDT;
    public byte DevType;
    public byte DevVerHi;
    public byte DevVerLo;
    public byte DevBleHL;
    public byte DevFlags;
    public int  CRCauth0;                   // Startwert (Scode Modulo1024)
    public int  CRCauth1;                   // DeviceChallangeCRC
    public int  CRCauth2;                   // AppChallangeCRC
    public int  CRCauth3;                   // DeviceResponseCRC
    public int  CRCauth4;                   // SendKeyDataCRC
    public final byte[] SessionKey;
    public byte[] authbuf;
    private byte[] sendkey;
    private static byte[] kdBlk1;
    private static byte[] kdBlk2;
    private static byte[] kdBlk3;
    private static byte[] kdBlk4;
    public  byte[] dhAuthRandomPrivat;
    public  byte[] dhAuthRandomPublic;
    public  byte[] dhAuthExternPublic;
    public  byte[] dhAuthExternSecret;
    public  byte[] dhAuthSharedSecret;
    public  byte[] dhAuthSessionExtern;
    public  byte[] dhSessionRandomPrivat;
    public  byte[] dhSessionRandomPublic;
    public  byte[] dhSessionExternPublic;
    public  byte[] dhSessionSharedSecret;
    public  byte[] dhBlockCipherRRCNT;


    /**
     * Instantiates a new Bluetooth LE device.
     *
     * @param device     a standard android Bluetooth device
     *
     */
    public MSTBleDevice(final BluetoothDevice device) {
        byte[] mDeviceData;
        mDevice = device;
        // TODO remove suppression
        @SuppressLint("MissingPermission") String mDeviceName = mDevice.getName();
        mIsLrcCorrect = false;
        mDeviceSystemCode = 0;
        mDeviceNumber = 0;
        mDeviceType = 0;
        mDevicePowerFlag = false;
        if(mDeviceName.length()==8){
            try {
                mDeviceData  = Base64.decode(mDeviceName.getBytes(), Base64.DEFAULT);
            }catch(UnknownError error) {
                mDeviceData = null;
                error.printStackTrace();
            }
            if((mDeviceData != null) && (mDeviceData.length == 6)){
                mIsLrcCorrect = checkLRC(mDeviceData);
                mDeviceSystemCode = unwrapSystemCode(mDeviceData);
                mDeviceNumber = unwrapDeviceNumber(mDeviceData);
                mDeviceType = unwrapDeviceType(mDeviceData);
                mDevicePowerFlag = unwrapPowerFlagIndicator(mDeviceData);
            }
        }

        authbuf = new byte[16];
        SessionKey = new byte[4];
        DevMAC = new byte[6];
        DevDT = new byte[6];

        dhAuthRandomPrivat = new byte[4];
        dhAuthRandomPublic = new byte[4];
        dhAuthExternPublic = new byte[4];
        dhAuthExternSecret = new byte[4];
        dhAuthSharedSecret = new byte[4];
        dhAuthSessionExtern = new byte[4];
        dhSessionRandomPrivat = new byte[4];
        dhSessionRandomPublic = new byte[4];
        dhSessionExternPublic = new byte[4];
        dhSessionSharedSecret = new byte[4];
        dhBlockCipherRRCNT = new byte[4];
    }

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    protected MSTBleDevice(Parcel in) {
        mDevice = in.readParcelable(BluetoothDevice.class.getClassLoader());
        AuthMode = in.readByte();
        AppType = in.readByte();
        AppVerHL = in.readByte();
        AppBleHL = in.readByte();
        AppFlags = in.readByte();
        DevMAC = in.readBlob();
        DevDT = in.readBlob();
        DevType = in.readByte();
        DevVerHi = in.readByte();
        DevVerLo = in.readByte();
        DevBleHL = in.readByte();
        DevFlags = in.readByte();
        CRCauth0 = in.readInt();
        CRCauth1 = in.readInt();
        CRCauth2 = in.readInt();
        CRCauth3 = in.readInt();
        CRCauth4 = in.readInt();
        SessionKey = in.readBlob();
        authbuf = in.readBlob();
        sendkey = in.readBlob();
        dhAuthRandomPrivat = in.readBlob();
        dhAuthRandomPublic = in.readBlob();
        dhAuthExternPublic = in.readBlob();
        dhAuthExternSecret = in.readBlob();
        dhAuthSharedSecret = in.readBlob();
        dhAuthSessionExtern = in.readBlob();
        dhSessionRandomPrivat = in.readBlob();
        dhSessionRandomPublic = in.readBlob();
        dhSessionExternPublic = in.readBlob();
        dhSessionSharedSecret = in.readBlob();
        dhBlockCipherRRCNT = in.readBlob();
    }

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(mDevice, flags);
        dest.writeByte(AuthMode);
        dest.writeByte(AppType);
        dest.writeByte(AppVerHL);
        dest.writeByte(AppBleHL);
        dest.writeByte(AppFlags);
        dest.writeBlob(DevMAC);
        dest.writeBlob(DevDT);
        dest.writeByte(DevType);
        dest.writeByte(DevVerHi);
        dest.writeByte(DevVerLo);
        dest.writeByte(DevBleHL);
        dest.writeByte(DevFlags);
        dest.writeInt(CRCauth0);
        dest.writeInt(CRCauth1);
        dest.writeInt(CRCauth2);
        dest.writeInt(CRCauth3);
        dest.writeInt(CRCauth4);
        dest.writeBlob(SessionKey);
        dest.writeBlob(authbuf);
        dest.writeBlob(sendkey);
        dest.writeBlob(dhAuthRandomPrivat);
        dest.writeBlob(dhAuthRandomPublic);
        dest.writeBlob(dhAuthExternPublic);
        dest.writeBlob(dhAuthExternSecret);
        dest.writeBlob(dhAuthSharedSecret);
        dest.writeBlob(dhAuthSessionExtern);
        dest.writeBlob(dhSessionRandomPrivat);
        dest.writeBlob(dhSessionRandomPublic);
        dest.writeBlob(dhSessionExternPublic);
        dest.writeBlob(dhSessionSharedSecret);
        dest.writeBlob(dhBlockCipherRRCNT);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<MSTBleDevice> CREATOR = new Creator<MSTBleDevice>() {
        // TODO remove suppression
        @SuppressLint("NewApi")
        @Override
        public MSTBleDevice createFromParcel(Parcel in) {
            return new MSTBleDevice(in);
        }

        @Override
        public MSTBleDevice[] newArray(int size) {
            return new MSTBleDevice[size];
        }
    };

    /**
     * check the LRC of devicename to indentify MST device.
     * @param data   the base64 decoded mDeviceName aka mDeviceData
     */
    private boolean checkLRC(final byte[] data) {
        byte calcLrc = (byte) (0xBE ^ data[0] ^ data[1] ^ data[2] ^ data[3] ^ data[4]);
        return (calcLrc == data[5]);
    }
    /**
     * unwraps the device Systemcode from the given device data.
     *
     * @param data   the base64 decoded mDeviceName aka mDeviceData
     */
    private int unwrapSystemCode(byte[] data){
        int hb,lb;
        hb = data[0];
        lb = data[1];
        if(hb < 0){hb = hb & 0xFF;}
        if(lb < 0){lb = lb & 0xFF;}
        return ((hb & 0xF) << 8) + lb;
    }

    /**
     * unwraps the device Number from the given device data.
     *
     * @param data   the base64 decoded mDeviceName aka mDeviceData
     */
    private int unwrapDeviceNumber(byte[] data){
        int hb,lb;
        hb = data[2];
        lb = data[3];
        if(hb < 0){hb = hb & 0xFF;}
        if(lb < 0){lb = lb & 0xFF;}
        return (hb << 4)  + (lb >> 4);
    }

    /**
     * unwraps the device Type from the given device data.
     *
     * @param data   the base64 decoded mDeviceName aka mDeviceData
     *     case 0 -> "Special Reader"
     *     case 1 -> "Guest Reader"
     *     case 2 -> "Staff Reader"
     *     default -> "Unknown Device";
     *
     */
    private int unwrapDeviceType(byte[] data){
        int lb;
        lb = data[3];
        if(lb < 0){lb = lb & 0xFF;}
        return (lb & 0x0F);
    }


    /**
     * unwraps the powerFlagIndicator from the given device data.
     *
     * @param data   the base64 decoded mDeviceName aka mDeviceData
     *     case 0 -> "Battery"
     *     case 1 -> "Permanent Power"
     *
     */
    private boolean unwrapPowerFlagIndicator(byte[] data){
        int lb;
        lb = data[4];
        if(lb < 0){lb = lb & 0xFF;}
        return  (lb & 0x80)==0x80;
    }

    public boolean getmIsLrcCorrect() {
        return mIsLrcCorrect;
    }

    public int getmDeviceSystemCode() {
        return mDeviceSystemCode;
    }

    public int getmDeviceNumber() {
        return mDeviceNumber;
    }

    public int getmDeviceType() {
        return mDeviceType;
    }

    public boolean getmDevicePowerFlag() {return mDevicePowerFlag;}

    public String getDT() {
        //2008-02-01T09:00:22+05

        return String.format("%d-%02d-%02dT%02d:%02d:%02d",DevDT[2]+2012,DevDT[1],DevDT[0],DevDT[3],DevDT[4],DevDT[5]);
    }

    /** Gets the device.
    *
    * @return the ble device
    */
    public BluetoothDevice getDevice() {
        return mDevice;
    }

    /**
     * Gets the scan record.
     *
     * @return the scan record
     */


    public boolean checkDeviceChallenge(byte[] deviceChallenge){
        return MSTUtils.DecodeDeviceChallenge(this, deviceChallenge);
    }

    public byte[] getAppResponseChallenge(){
        if(AuthMode==2) MSTUtils.EncodeAppResponseChallengeL2(this);
        if(AuthMode==3) MSTUtils.EncodeAppResponseChallengeL3(this);
        byte[] response = new byte[17];
        response[0] = 0x00;
        System.arraycopy(authbuf, 0, response, 1, 16);
        return response;
    }

    public boolean checkDecodeDeviceResponse(byte[] deviceResponse){
        if((AuthMode==3) && MSTUtils.DecodeDeviceResponseL3(this, deviceResponse)) return(true);
        return (AuthMode == 2) && MSTUtils.DecodeDeviceResponseL2(this, deviceResponse);
    }

    public boolean encodeSendKeyData(byte[] keydata){
        sendkey = MSTUtils.EncodeSendKeyData(keydata, this);

        if (sendkey.length == 64) {
            kdBlk1 = setKeydata(0);
            kdBlk2 = setKeydata(1);
            kdBlk3 = setKeydata(2);
            kdBlk4 = setKeydata(3);
            return true;
        }
        if (sendkey.length == 48) {
            kdBlk1 = setKeydata(0);
            kdBlk2 = setKeydata(1);
            kdBlk3 = setKeydata(2);
            kdBlk4 = new byte[1];
            return true;
        }

        return false;
    }

    /**
     * returns block of the keydata to open the door
     * for ble scan purposes
     * not necessary to display in gui
     * @return byte[] keydata block
     */
    public byte[] getKeydataBlk(int blkCnt){
        byte[] newarray;

        switch (blkCnt){
            case 0:
                newarray = kdBlk1;
                break;
            case 1:
                newarray = kdBlk2;
                break;
            case 2:
                newarray = kdBlk3;
                break;
            case 3:
                newarray = kdBlk4;
                break;
            default:
                newarray = new byte[1];
        }

        return newarray;
    }

    private byte[] setKeydata(int blkCnt){
        byte[] result = new byte[17];

        result[0] = (byte) (((sendkey.length>48) ? 0x30:0x20)+blkCnt);
        System.arraycopy(sendkey, (16 * blkCnt), result, 1, 16);
        return result;
    }

}
