plugins {
    id("common.library")
    id("android.target.library")
    id("jvm.target.library")
    // id("ios.target.library")
}

commonDependencies {
    implementation(project(":core-common"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":core-lock-common"))
    api(project(":core-location-common"))
    api(project(":core-lock-airbnk"))
    api(project(":core-lock-iseo"))
    api(project(":core-lock-mst"))
    api(project(":core-lock-rayonics"))
    api(project(":core-lock-tedee"))
    api(project(":core-lock-ttlock"))
}