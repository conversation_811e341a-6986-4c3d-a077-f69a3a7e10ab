package core.permissions.manager.models

import android.Manifest
import android.app.Activity
import android.os.Build
import android.util.Log
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import core.permissions.manager.PermissionsManager
import core.permissions.manager.android
import core.permissions.manager.usecases.RequestIgnoreBatteryOptimizationUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch

internal class PermissionHolder(
    private val permission: Permission,
    private val owner: LifecycleOwner
) {

    private val callback = MutableSharedFlow<List<Response>>()
    private val launcher: ActivityResultLauncher<Array<String>> = getLauncher()

    suspend fun request(manager: PermissionsManager): List<Response> {
        return when (permission) {
            Permission.BypassBatteryRestrictions -> {
                RequestIgnoreBatteryOptimizationUseCase().invoke(owner, manager)
            }

            Permission.Camera -> {
                permissionResponse()
            }

            Permission.Location -> {
                permissionResponse()
            }

            Permission.LocationBackground -> {
                permissionResponse()
            }

            Permission.LocationPrecise -> {
                permissionResponse()
            }

            Permission.Notification -> {
                permissionResponse()
            }

            Permission.Bluetooth -> {
                permissionResponse()
            }

            Permission.BluetoothConnect -> {
                permissionResponse()
            }

            Permission.LocationSettingsOptimization -> {
                TODO()
            }
        }
    }

    private suspend fun permissionResponse(): List<Response> {
        try {
            launcher.launch(permission.androidList())
        } catch (ex: Exception) {
            Log.e("PermissionHolder", ex.message, ex)
            return permission.androidList().map { Response(it.toPermission(), Response.Result.Denied) }
        }
        return callback.take(1).last()
    }

    private fun getLauncher(): ActivityResultLauncher<Array<String>> {
        val caller = owner as ActivityResultCaller
        val callback = ActivityResultCallback<Map<String, Boolean>> { results ->
            CoroutineScope(Dispatchers.Default).launch {
                callback.emit(results.map { it.toResponse(owner) })
            }
        }

        return caller.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
            callback
        )
    }

    private fun Map.Entry<String, Boolean>.toResponse(
        owner: LifecycleOwner
    ): Response {
        val grantType = if (value) {
            Response.Result.Granted
        } else if (!value && isShowRational(permission = key, owner = owner)) {
            Response.Result.PermanentlyDenied
        } else {
            Response.Result.Denied
        }

        return Response(
            permission = key.toPermission(),
            result = grantType
        )
    }

    private
    fun isShowRational(permission: String, owner: LifecycleOwner): Boolean {
        return when (owner) {
            is Fragment -> {
                owner.shouldShowRequestPermissionRationale(permission)
            }

            is Activity -> {
                owner.shouldShowRequestPermissionRationale(permission)
            }

            else -> true
        }
    }

    private fun String.toPermission(): Permission {
        return when {
            this == Manifest.permission.ACCESS_FINE_LOCATION -> {
                Permission.LocationPrecise
            }

            this == Manifest.permission.ACCESS_COARSE_LOCATION -> {
                Permission.Location
            }

            this == Manifest.permission.ACCESS_BACKGROUND_LOCATION -> {
                Permission.LocationBackground
            }

            this == Manifest.permission.CAMERA -> {
                Permission.Camera
            }

            this == Manifest.permission.POST_NOTIFICATIONS -> {
                Permission.Notification
            }

            Permission.Bluetooth.android().contains(this) -> {
                Permission.Bluetooth
            }

            else -> throw Exception("UnHandled permission: $this")
        }
    }

    private fun Permission.androidList(): Array<String> {
        return when (this) {
            Permission.BypassBatteryRestrictions -> {
                arrayOf()
            }

            Permission.LocationSettingsOptimization -> {
                arrayOf()
            }

            Permission.Camera -> {
                arrayOf(Manifest.permission.CAMERA)
            }

            Permission.Location -> {
                arrayOf(Manifest.permission.ACCESS_COARSE_LOCATION)
            }

            Permission.LocationPrecise -> {
                arrayOf(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
            }

            Permission.LocationBackground -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
                } else {
                    arrayOf()
                }
            }

            Permission.Bluetooth -> {
                Permission.Bluetooth.android().toTypedArray()
            }

            Permission.Notification -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS)
                } else {
                    arrayOf()
                }
            }

            Permission.BluetoothConnect -> {
                Permission.BluetoothConnect.android().toTypedArray()
            }
        }
    }
}