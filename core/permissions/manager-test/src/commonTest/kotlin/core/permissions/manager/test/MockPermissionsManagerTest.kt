package core.permissions.manager.test

import core.monitoring.common.test.CLILogger
import core.permissions.manager.models.Permission
import core.permissions.manager.models.Response
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class MockPermissionsManagerTest {

    private val manager = MockPermissionsManager(CLILogger(Any()))

    @Test
    fun permissionsChecks() = runBlocking {
        assertEquals(false, manager.check(Permission.Camera))

        val sample = listOf(Response(Permission.Camera, Response.Result.Denied))
        assertEquals(sample, manager.request(listOf(Permission.Camera)))

        val success = listOf(
            Response(Permission.Camera, Response.Result.Granted)
        )
        manager.setup(mapOf(Permission.Camera to Response.Result.Granted))
        assertEquals(success, manager.request(listOf(Permission.Camera)))
    }
}