## Please add these rules to your existing keep rules in order to suppress warnings.
## This is generated automatically by the Android Gradle plugin.
#-dontwarn org.bouncycastle.jsse.BCSSLParameters
#-dontwarn org.bouncycastle.jsse.BCSSLSocket
#-dontwarn org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
#-dontwarn org.conscrypt.Conscrypt$Version
#-dontwarn org.conscrypt.Conscrypt
#-dontwarn org.openjsse.javax.net.ssl.SSLParameters
#-dontwarn org.openjsse.javax.net.ssl.SSLSocket
#-dontwarn org.openjsse.net.ssl.OpenJSSE
#-dontwarn org.slf4j.impl.StaticLoggerBinder
#-dontwarn android.os.ServiceManager*
#-dontwarn com.bun.miitmdid.core.MdidSdkHelper*
#-dontwarn com.bun.miitmdid.interfaces.IIdentifierListener*
#-dontwarn com.bun.miitmdid.interfaces.IdSupplier*
#-dontwarn com.google.firebase.iid.FirebaseInstanceId*
#-dontwarn com.google.firebase.iid.InstanceIdResult*
#-dontwarn com.huawei.hms.ads.identifier.AdvertisingIdClient$Info*
#-dontwarn com.huawei.hms.ads.identifier.AdvertisingIdClient*
#-dontwarn com.tencent.android.tpush.otherpush.OtherPushClient*
#-dontwarn com.android.org.conscrypt.SSLParametersImpl
#
#-keep class android.support.v7.widget.SearchView { *; }
#-keepclassmembers class  io.reactivex.schedulers.Schedulers{  public *; }
## Models
#-keepclassmembers class com.example.models.** {*;}
#
## GSON
#-keepattributes Signature
#-keepattributes *Annotation*
#-dontwarn sun.misc.**
#-keep class com.google.gson.examples.android.model.** { *; }
#-keep class * implements com.google.gson.TypeAdapterFactory
#-keep class * implements com.google.gson.JsonSerializer
#-keep class * implements com.google.gson.JsonDeserializer
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# JSR 305 annotations are for embedding nullability information.
#-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
#-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
#-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

-dontwarn org.slf4j.impl.StaticLoggerBinder
-printconfiguration

-keepnames class core.** { *; }
-keepnames class core.permissions.** { *; }
-keepnames class data.** { *; }
-keepnames class domain.** { *; }
-keepnames class feature.** { *; }
#-keepclasseswithmembernames,includedescriptorclasses class * {
#    native <methods>;
#}
#
#
##-keepclassmembers,allowoptimization enum * {
##    public static **[] values();
##    public static ** valueOf(java.lang.String);
##}
#-keepclassmembers public class com.app.keyless {
#    public void setMyProperty(int);
#    public int getMyProperty();
#}
#
#-keepclassmembers public class com.app.keyless
#
#
#-keepclassmembers class mybeans.** {
#    void set*(***);
#    void set*(int, ***);
#
#    boolean is*();
#    boolean is*(int);
#
#    *** get*();
#    *** get*(int);
#}

-keep class com.rayo.logic.sdk.** { *; }
-keep class rayo.logicsdk.ble.** { *; }
-keep class rayo.logicsdk.data.** { *; }
-keep class rayo.logicsdk.bean.** { *; }
-keep class com.iseo.v364sdk.** { *; }
-keep class org.msgpack.core.buffer.** { *; }
# keep this around for some enums that ACRA needs
#-keep class rayo.logicsdk.ble.BluetoothLeControl { *; }
#-keepnames class rayo.logicsdk.ble.BluetoothLeControl$** {*;}
-dontwarn com.rayo.logic.sdk.**
-dontwarn com.iseo.v364sdk.**

-keep public  class com.ttlock.bl.sdk.** {*;}
-keep public class **.*entity*.** {*;}
-keep public class **.*constant*.** {*;}

-keep class core.common.encryption.AESHelper { *; }
-dontwarn java.lang.invoke.StringConcatFactory

#-dontshrink

#-dontoptimize
#
#-dontobfuscate
#
#-keep,allowoptimization,allowobfuscation class org.apache.**
-dontwarn com.google.android.gms.**
-keep class com.google.android.gms.** { *; }
-keep class com.google.firebase.** { *; }
-keep class com.wdullaer.materialdatetimepicker.** { *; }

# AirBnk keeps
-keep class com.airbnk.** { *; }
-dontwarn com.tamic.novate.BaseApiService
-dontwarn com.tamic.novate.Throwable
-dontwarn com.tamic.novate.cache.CookieCache
-dontwarn com.tamic.novate.cache.CookieCacheImpl
-dontwarn com.tamic.novate.config.ConfigLoader
-dontwarn com.tamic.novate.cookie.CookiePersistor
-dontwarn com.tamic.novate.cookie.NovateCookieManager
-dontwarn com.tamic.novate.cookie.SharedPrefsCookiePersistor
-dontwarn com.tamic.novate.download.DownLoadCallBack
-dontwarn com.tamic.novate.download.DownSubscriber
-dontwarn com.tamic.novate.exception.NovateException
-dontwarn com.tamic.novate.request.RequestInterceptor
-dontwarn com.tamic.novate.util.FileUtil
-dontwarn com.tamic.novate.util.LogWraper
-dontwarn com.tamic.novate.util.Utils
-dontwarn retrofit2.adapter.rxjava.RxJavaCallAdapterFactory
-dontwarn rx.Observable$Transformer
-dontwarn rx.Observable
-dontwarn rx.Scheduler
-dontwarn rx.Subscriber
-dontwarn rx.Subscription
-dontwarn rx.android.schedulers.AndroidSchedulers
-dontwarn rx.functions.Action0
-dontwarn rx.functions.Func1
-dontwarn rx.schedulers.Schedulers
